package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * (MesModulRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Data
@TableName(value = "t_mes_modul_record")
public class MesModulRecord implements Serializable {
    private static final long serialVersionUID = 726379708167042526L;
            
    @TableId
    private Integer id;
    
    /**
    * 模具id
    */
    @TableField(value="modul_id")
    private Integer modulId;

    /**
     * 模具名称
     */
    @TableField(exist = false)
    private String modulName;
    /**
     * 模具编码
     */
    @TableField(exist = false)
    private String modulCode;
    /**
    * 设备id
    */
    @TableField(value="equipment_id")
    private Integer equipmentId;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;
    /**
    * 产品id
    */
    @TableField(value="production_id")
    private Integer productionId;
    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productionName;
    /**
    * 每模产品数
    */
    @TableField(value="production_multiple")
    private Integer productionMultiple;
    /**
    * 开始使用时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @TableField(value="start_time")
    private Date startTime;
    /**
    * 卸载时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @TableField(value="end_time")
    private Date endTime;
    /**
    * 负载时间
    */
    @TableField(value="load_time")
    private Double loadTime;
    /**
    * 载荷时间
    */
    @TableField(value="function_time")
    private Double functionTime;
    /**
    * 生产次数
    */
    @TableField(value="product_times")
    private Integer productTimes;
    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="create_time",fill = FieldFill.INSERT)
    private Date createTime;
    /**
    * 创建人
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建部门
    */
    @TableField(value="dept_id",fill = FieldFill.INSERT)
    private String deptId;

    @TableField(exist = false)
    private String createUserName;

}
