package com.boyo.mes.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.entity.MesModulproduction;
import com.boyo.mes.mapper.MesModulRecordMapper;
import com.boyo.mes.mapper.MesModulproductionMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesMould;
import com.boyo.mes.mapper.MesMouldMapper;
import com.boyo.mes.service.IMesMouldService;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模具基本信息(MesMould)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Service("mesMouldService")
@AllArgsConstructor
@Tenant
public class MesMouldServiceImpl extends ServiceImpl<MesMouldMapper, MesMould> implements IMesMouldService {
    private final MesMouldMapper mesMouldMapper;
    private final MesModulproductionMapper mesModulproductionMapper;
    private final MesModulRecordMapper mesModulRecordMapper;
    private final IoTDBUtil ioTDBUtil;
    private final IotEquipmentMapper equipmentMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesMould> selectMesMouldList(MesMould mesMould) {
        List<MesMould> list = mesMouldMapper.selectMesMouldList(mesMould);
        if(mesMould.isLife()){
            for (MesMould obj:list) {
                QueryWrapper<MesModulRecord> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("modul_id",obj.getId()).isNull("end_time");
                List<MesModulRecord> records = mesModulRecordMapper.selectList(queryWrapper);
                if(records != null && records.size() > 0){
                    MesModulRecord record = records.get(0);
                    IotEquipment equipment = equipmentMapper.selectById(record.getEquipmentId());
                    if(ObjectUtil.isNotNull(equipment)){
                        double tagCount = ioTDBUtil.getTagCount(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(),equipment.getEquipmentCode(),"Product_Status",DateUtil.formatDateTime(record.getStartTime()),DateUtil.formatDateTime(new Date()));
                        if(ObjectUtil.isNull(obj.getUsedLife())){
                            obj.setUsedLife(NumberUtil.round(tagCount ,2).doubleValue());
                        }else{
                            obj.setUsedLife(NumberUtil.round(Convert.toDouble(obj.getUsedLife()) + tagCount,2).doubleValue());
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public boolean updateById(MesMould entity) {
        super.updateById(entity);
        QueryWrapper<MesModulproduction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("modul_id",entity.getId());
        mesModulproductionMapper.delete(queryWrapper);
        if(StrUtil.isNotEmpty(entity.getProductionIdStr())) {
            String[] pids = entity.getProductionIdStr().split(",");
            if (pids.length > 0) {
                for (String id : pids) {
                    MesModulproduction obj = new MesModulproduction();
                    obj.setModulId(entity.getId());
                    obj.setProductionId(Convert.toInt(id));
                    mesModulproductionMapper.insert(obj);
                }
            }
        }
        return true;
    }

    @Override
    public boolean save(MesMould entity) {
        super.save(entity);
        if(StrUtil.isNotEmpty(entity.getProductionIdStr())){
            String[] pids = entity.getProductionIdStr().split(",");
            if(pids.length > 0){
                for (String id:pids) {
                    MesModulproduction obj = new MesModulproduction();
                    obj.setModulId(entity.getId());
                    obj.setProductionId(Convert.toInt(id));
                    mesModulproductionMapper.insert(obj);
                }
            }
        }
        return true;
    }

    @Override
    public MesMould getById(Serializable id) {
        MesMould mesMould = new MesMould();
        mesMould.setId(Convert.toInt(id));
        List<MesMould> list = mesMouldMapper.selectMesMouldList(mesMould);
        return list.get(0);
    }
}