package com.boyo.iot.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.List;

import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BoyoBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 系统工单(WorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2022-04-08 15:10:05
 */
@Data
@TableName(value = "t_work_order")
public class WorkOrder extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -51719842040638420L;
            
    @TableId
    private Integer id;
    
    /**
    * 工单类型 如IoT
    */
    @TableField(value="type")
    private String type;
    /**
    * 工单编号
    */
    @Excel(name = "工单编号")
    @TableField(value="num")
    private String num;
    /**
    * 对应故障
    */
    @TableField(value="fault_id")
    private Integer faultId;
    /**
    * 工单描述
    */
    @Excel(name = "工单描述")
    @TableField(value="order_msg")
    private String orderMsg;
    /**
    * 负责人id
    */
    @Excel(name = "工单负责人")
    @TableField(value="owner_user_id",updateStrategy = FieldStrategy.IGNORED)
    private Long ownerUserId;

    @TableField(value = "reason_id")
    private int reasonId;

    @TableField(exist = false)
    private String reasonStr;

    @TableField(exist = false)
    private String ownerUserName;

    @TableField(exist = false)
    private String createUserName;
    /**
    * 接收时间
    */
    @TableField(value="receive_time",updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间")
    private Date receiveTime;
    /**
    * 完成时间
    */
    @TableField(value="complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间")
    private Date completeTime;

    @TableField(value = "close_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date closeTime;

    @TableField(value = "close_user_id")
    private Long closeUserId;

    @TableField(exist = false)
    private String closeUserName;
    /**
    * 工单完工内容
    */
    @TableField(value="remark")
    private String remark;

    @TableField(value = "create_user_id")
    private Long createUserId;
    @TableField(value = "dept_id")
    private String deptId;


    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 故障发生时图片
     */
    @TableField(value = "order_img")
    private String orderImg;

    /**
     * 完工图片
     */
    @TableField(value = "complete_img")
    private String completeImg;

    @TableField(value = "equipment_id")
    private Integer equipmentId;

    @TableField(value = "happen_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发生时间")
    private Date happenTime;

    /**
     * 物料清单
     */
    @TableField(exist = false)
    private List<OrderMaterial> materialList;

}
