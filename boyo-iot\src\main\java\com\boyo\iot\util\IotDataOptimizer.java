package com.boyo.iot.util;

import com.boyo.iot.entity.IorealData;
import com.boyo.iot.mapper.IorealDataMapper;
import com.boyo.iot.service.IotRealDataBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * IoT数据优化工具类
 * 提供多种优化策略来处理频繁的数据更新
 */
@Component
@Slf4j
public class IotDataOptimizer {

    @Autowired
    private IorealDataMapper iorealDataMapper;

    @Autowired
    private IotRealDataBatchService batchService;

    // 配置参数
    @Value("${iot.data.batch.enabled:true}")
    private boolean batchEnabled;

    @Value("${iot.data.batch.threshold:50}")
    private int batchThreshold;

    @Value("${iot.data.update.ratio:0.8}")
    private double updateRatio; // 更新比例阈值

    // 缓存已存在的key，减少数据库查询
    private final Set<String> existingKeys = ConcurrentHashMap.newKeySet();
    private volatile long lastCacheRefresh = 0;
    private static final long CACHE_REFRESH_INTERVAL = 300000; // 5分钟刷新一次缓存

    /**
     * 优化的保存或更新方法
     * 根据数据特征选择最优的处理策略
     */
    public void saveOrUpdateOptimized(List<IorealData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        try {
            // 如果启用批量处理且数据量较小，使用异步批量处理
            if (batchEnabled && dataList.size() <= batchThreshold) {
                batchService.addDataBatch(dataList);
                return;
            }

            // 对于大批量数据，使用同步优化处理
            processSyncOptimized(dataList);

        } catch (Exception e) {
            log.error("优化保存更新失败，回退到原始方法，数据量: {}, 错误: {}", 
                dataList.size(), e.getMessage(), e);
            // 回退到原始方法
            iorealDataMapper.saveOrUpdate(dataList);
        }
    }

    /**
     * 同步优化处理
     */
    private void processSyncOptimized(List<IorealData> dataList) {
        // 刷新缓存（如果需要）
        refreshCacheIfNeeded();

        // 分析数据特征
        DataAnalysis analysis = analyzeData(dataList);

        if (analysis.updateRatio > updateRatio) {
            // 更新为主的场景：先尝试批量更新，再插入新记录
            processUpdateFirst(dataList, analysis);
        } else {
            // 插入为主的场景：使用原有的 ON DUPLICATE KEY UPDATE
            iorealDataMapper.saveOrUpdate(dataList);
        }

        // 更新缓存
        for (IorealData data : dataList) {
            existingKeys.add(data.getKey());
        }
    }

    /**
     * 更新优先的处理策略
     */
    private void processUpdateFirst(List<IorealData> dataList, DataAnalysis analysis) {
        try {
            // 先批量更新已存在的记录
            if (!analysis.existingData.isEmpty()) {
                iorealDataMapper.batchUpdateOptimized(analysis.existingData);
            }

            // 再插入新记录
            if (!analysis.newData.isEmpty()) {
                iorealDataMapper.batchInsertNew(analysis.newData);
            }

            log.debug("更新优先处理完成，更新: {}, 插入: {}", 
                analysis.existingData.size(), analysis.newData.size());

        } catch (Exception e) {
            log.warn("更新优先处理失败，回退到标准方法: {}", e.getMessage());
            // 回退到标准方法
            iorealDataMapper.saveOrUpdate(dataList);
        }
    }

    /**
     * 分析数据特征
     */
    private DataAnalysis analyzeData(List<IorealData> dataList) {
        DataAnalysis analysis = new DataAnalysis();

        for (IorealData data : dataList) {
            if (existingKeys.contains(data.getKey())) {
                analysis.existingData.add(data);
            } else {
                analysis.newData.add(data);
            }
        }

        analysis.updateRatio = dataList.isEmpty() ? 0 : 
            (double) analysis.existingData.size() / dataList.size();

        return analysis;
    }

    /**
     * 刷新缓存（如果需要）
     */
    private void refreshCacheIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCacheRefresh > CACHE_REFRESH_INTERVAL) {
            synchronized (this) {
                if (currentTime - lastCacheRefresh > CACHE_REFRESH_INTERVAL) {
                    refreshExistingKeysCache();
                    lastCacheRefresh = currentTime;
                }
            }
        }
    }

    /**
     * 刷新已存在key的缓存
     */
    private void refreshExistingKeysCache() {
        try {
            // 这里可以根据需要实现缓存刷新逻辑
            // 例如：定期从数据库加载所有key，或者使用Redis缓存
            log.debug("刷新key缓存");
        } catch (Exception e) {
            log.warn("刷新key缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 强制刷新批量处理队列
     */
    public void flush() {
        if (batchEnabled) {
            batchService.flush();
        }
    }

    /**
     * 获取优化统计信息
     */
    public String getOptimizationStats() {
        if (batchEnabled) {
            return "批量处理统计: " + batchService.getStatistics().toString();
        } else {
            return "批量处理未启用";
        }
    }

    /**
     * 数据分析结果
     */
    private static class DataAnalysis {
        List<IorealData> existingData = new java.util.ArrayList<>();
        List<IorealData> newData = new java.util.ArrayList<>();
        double updateRatio = 0.0;
    }

    /**
     * 直接使用批量服务（用于替换原有调用）
     */
    public void addToBatch(List<IorealData> dataList) {
        if (batchEnabled) {
            batchService.addDataBatch(dataList);
        } else {
            iorealDataMapper.saveOrUpdate(dataList);
        }
    }
}
