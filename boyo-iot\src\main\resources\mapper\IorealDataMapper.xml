<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IorealDataMapper">

    <resultMap type="com.boyo.iot.entity.IorealData" id="IorealDataResult">
        <result property="key" column="key"/>
        <result property="deviceCode" column="device_code"/>
        <result property="tag" column="tag"/>
        <result property="val" column="val"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectIorealDataList" parameterType="com.boyo.iot.entity.IorealData" resultMap="IorealDataResult">
        select
        `key`, device_code, tag, val,update_time
        from iot_real_data
        <where>
            <if test="deviceCode != null and deviceCode != ''">
                and device_code = #{deviceCode}
            </if>
            <if test="tag != null and tag != ''">
                and tag = #{tag}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
    <!-- 优化后的批量保存方法 -->
    <insert id="saveOrUpdate">
        INSERT INTO iot_real_data(`key`, device_code, tag, val, update_time)
        VALUES
        <foreach collection="list" item="p" index="index" separator=",">
            (#{p.key}, #{p.deviceCode}, #{p.tag}, #{p.val}, NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        val = VALUES(val),
        update_time = VALUES(update_time)
    </insert>

    <!-- 针对更新频繁场景的优化方法 -->
    <update id="batchUpdateOptimized">
        <foreach collection="list" item="item" separator=";">
            UPDATE iot_real_data
            SET val = #{item.val}, update_time = NOW()
            WHERE `key` = #{item.key}
        </foreach>
    </update>

    <!-- 批量插入新记录 -->
    <insert id="batchInsertNew">
        INSERT IGNORE INTO iot_real_data(`key`, device_code, tag, val, update_time)
        VALUES
        <foreach collection="list" item="p" index="index" separator=",">
            (#{p.key}, #{p.deviceCode}, #{p.tag}, #{p.val}, NOW())
        </foreach>
    </insert>
<!--    <insert id="saveOrUpdate">-->
<!--        insert into iot_real_data(`key`,device_code, tag, val,update_time)-->
<!--        values-->
<!--        <foreach collection="list" item="p" index="index" separator=",">-->
<!--            (-->
<!--            #{p.key},-->
<!--            #{p.deviceCode},-->
<!--            #{p.tag},-->
<!--            #{p.val},-->
<!--            NOW())-->
<!--        </foreach>-->
<!--        ON DUPLICATE KEY UPDATE-->
<!--        device_code = values(device_code),-->
<!--        tag = values(tag),-->
<!--        val = values(val),-->
<!--        update_time = values(update_time)-->
<!--    </insert>-->
</mapper>

