package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipInspectionSpotItem;
import com.boyo.eam.domain.Media;
import com.boyo.eam.service.IEquipInspectionSpotItemService;
import com.boyo.eam.service.IMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 点检-项目(EquipInspectionSpotItem)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Api("点检-项目")
@RestController
@RequestMapping("/equip/equipInspectionSpotItem")
@AllArgsConstructor
public class EquipInspectionSpotItemController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipInspectionSpotItemService equipInspectionSpotItemService;
    private final IMediaService mediaService;
    /**
     * 查询点检-项目列表
     *
     */
    @ApiOperation("查询点检-项目列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipInspectionSpotItem equipInspectionSpotItem) {
        startPage();
        List<EquipInspectionSpotItem> list = equipInspectionSpotItemService.selectEquipInspectionSpotItemList(equipInspectionSpotItem);
        return getDataTable(list);
    }
    
    /**
     * 获取点检-项目详情
     */
    @ApiOperation("获取点检-项目详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipInspectionSpotItemService.getById(id));
    }

    /**
     * 新增点检-项目
     */
    @ApiOperation("新增点检-项目")
    @PostMapping
    public AjaxResult add(@RequestBody EquipInspectionSpotItem equipInspectionSpotItem) {
        return toBooleanAjax(equipInspectionSpotItemService.save(equipInspectionSpotItem));
    }

    /**
     * 修改点检-项目
     */
    @ApiOperation("修改点检-项目")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipInspectionSpotItem equipInspectionSpotItem) {
        return toBooleanAjax(equipInspectionSpotItemService.updateById(equipInspectionSpotItem));
    }

    /**
     * 删除点检-项目
     */
    @ApiOperation("删除点检-项目")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipInspectionSpotItemService.removeByIds(Arrays.asList(ids)));
    }


    // 移动端

    /**
     * 查询点检-项目列表
     *
     */
    @ApiOperation("查询点检-项目列表")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipInspectionSpotItem equipInspectionSpotItem) {
        startPage();
        List<EquipInspectionSpotItem> list = equipInspectionSpotItemService.selectEquipInspectionSpotItemList(equipInspectionSpotItem);
        return getDataTable(list);
    }

    /**
     * 获取点检-项目详情
     */
    @ApiOperation("获取点检-项目详情")
    @GetMapping(value = "/pad/{id}")
    public AjaxResult padGetInfo(@PathVariable("id") Integer id) {
        EquipInspectionSpotItem item = equipInspectionSpotItemService.getItemAndRecord(id);
        if (item!=null){
            // 通过记录中的图片id查询图片地址
            String mediaIds = item.getMediaId();
            if (mediaIds!=null && !"".equals(mediaIds)){
                List<String> mediaUrls = new ArrayList<>();
                String[] mediaIdArray = mediaIds.split(",");
                List<Media> mediaList = mediaService.list(
                        Wrappers.<Media>lambdaQuery()
                                .in(Media::getId, mediaIdArray)
                );
                for (Media media:mediaList){
                    mediaUrls.add(media.getUrl());
                }
                item.setMediaUrls(mediaUrls);
            }
            return AjaxResult.success(item);
        }
        return AjaxResult.error("未找到id");
    }
}
