package com.boyo.web.controller.haihui;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTsl;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.service.IIotTslService;
import com.boyo.iot.util.IoTDBUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@RestController
@RequestMapping("/haihui/screen")
public class HaiHuiScreenController extends BaseController {

    @Resource
    private IIotEquipmentService iotEquipmentService;

    @Resource
    private IIotEquipmentService equipmentService;

    @Resource
    private IIotTslService iotTslService;

    @Resource
    private IoTDBUtil ioTDBUtil;
    final String TENANT = "2db4bfe2cbe14269b410e4747073ee47";


    @ApiOperation("查询IoT物模型列表")
    @GetMapping("/shopwork/list")
    public TableDataInfo list(IotTsl iotTsl) {
        startPage();
        List<IotTsl> list = iotTslService.selectIotTslList(iotTsl);
        return getDataTable(list);
    }

    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/device/list")
    public TableDataInfo listOnly(IotEquipment iotEquipment) {
//        iotEquipment.setDeptId(TENANT);
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        return getDataTable(list);
    }

    @ApiOperation("数据概览")
    @GetMapping("/data/overview")
    public JSONObject list(IotEquipment iotEquipment) {
        JSONObject result = new JSONObject();
        int onlineCount = 0;
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                for (IotTslAttr attr : temp.getAttrList()) {
//                    if ((attr.getAttrType().equalsIgnoreCase("Double") || attr.getAttrType().equalsIgnoreCase("Integer")) && StrUtil.isNotEmpty(attr.getLastVal())) {
//                        attr.setLastVal(Convert.toStr(NumberUtil.round(Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple(), 2)));
//                    }
                    if ("开机".equals(attr.getAttrName()) && attr.getLastUpdateTime() != null) {
                        if ("1".equals(attr.getLastVal())) {
                            onlineCount++;
                            break;
                        }
                    }
                }
            }
        }
        try {
            final long energy = getEnergy(DateUtil.beginOfDay(new Date()), new Date());
            result.put("todayEnergy", energy);
        } catch (Exception e) {
            e.printStackTrace();
        }
        int total = list.size();
        result.put("devicesCount", total);
        result.put("onlineCount", onlineCount);
        result.put("offlineCount", total - onlineCount);
//        result.put("list", list);
        return result;
    }

    @ApiOperation("设备运行情况")
    @GetMapping("/device/work/status")
    public JSONObject workStatus(IotEquipment iotEquipment) {
        JSONObject result = new JSONObject();
        float onlineCount = 0;
        float offlineCount = 0;
        float errorlineCount = 0;
        long openTime = 0;
        long stopTime = 0;
        long errorTime = 0;

        List<JSONObject> ll = new ArrayList<>();
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                for (IotTslAttr attr : temp.getAttrList()) {
                    if ("开机".equals(attr.getAttrName()) && attr.getLastUpdateTime() != null) {
                        if ("0".equals(attr.getLastVal())) {
                            offlineCount++;
//                            break;
                        } else if ("1".equals(attr.getLastVal())) {
                            onlineCount++;
//                            break;
                        } else {
                            errorlineCount++;
//                            break;
                        }
                    }
                }

                JSONArray time = getTime(temp.getEquipmentCode());
                if (time != null) {
                    for (int i = 0; i < time.size(); i++) {
                        JSONObject jsonObject = time.getJSONObject(i);
                        final Object power_on = jsonObject.get("Power_ON");
                        if (power_on != null && !"".equals(power_on)) {
                            if ("1.0".equals(power_on.toString())) {
                                openTime++;
                            }
                            if ("0.0".equals(power_on.toString())) {
                                stopTime++;
                            }
                        } else {
                            errorTime++;
                        }
                    }
                }
//                ll.add(time);
            }
        }
        float total = offlineCount + onlineCount + errorlineCount;

        result.put("onlineCount", onlineCount / total);
        result.put("offlineCount", offlineCount / total);
        result.put("errorlineCount", errorlineCount / total);


        result.put("openTime", (int) (openTime / 60));
        result.put("stopTime", (int) (stopTime / 60));
        result.put("errorTime", (int) (errorTime / 60));


        return result;
    }

    @ApiOperation("日用电量统计")
    @GetMapping("/elect/energy")
    public JSONObject getPEnum(String tag) {
        JSONObject ans = new JSONObject();
        Map<String, Long> energyMap = new HashMap<>();
        Date start = DateUtil.beginOfDay(new Date());
        Date end = new Date();
        long energy = getEnergy(start, end);
        energyMap.put(DateUtil.format(start, "yyyy/MM/dd"), energy);
        for (int i = 0; i < 7; i++) {
            end = start;
            start = DateUtil.offsetDay(start, -1);
            long energytemp = getEnergy(start, end);
            energyMap.put(DateUtil.format(start, "yyyy/MM/dd"), energytemp);
        }
        ans.put("data", energyMap);
        return ans;
    }
    @GetMapping("bank/equipment/currentInfo")
    public TableDataInfo listOnly1test(IotEquipment iotEquipment) {
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                for (IotTslAttr attr : temp.getAttrList()) {
                    if ((attr.getAttrType().equalsIgnoreCase("Double") || attr.getAttrType().equalsIgnoreCase("Integer")) && StrUtil.isNotEmpty(attr.getLastVal())) {
                        attr.setLastVal(Convert.toStr(NumberUtil.round(Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple(), 2)));
                    }
                    if (("工作状态".equals(attr.getAttrName())||"绿灯".equals(attr.getAttrName()))&&attr.getLastUpdateTime()!=null) {
                        final Date lastUpdateTime = attr.getLastUpdateTime();
                        final long between = DateUtil.between(lastUpdateTime, new Date(), DateUnit.MINUTE);
                        if (between > 5L) {
                            attr.setLastVal("0");
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    @ApiOperation("生产次数")
    @GetMapping("/production/frequency")
    public AjaxResult frequency(String device) {
        String tag = "Number";
        Date start = DateUtil.beginOfDay(new Date());
        Date end = new Date();
        JSONObject ans = new JSONObject();
        for (int i = 0; i < 10; i++) {
            if (i != 0) {
                end = start;
                start = DateUtil.offsetDay(start, -1);
            }
            try {
                List<HistoryData> list = ioTDBUtil.listData(TENANT, device, tag, DateUtil.formatDateTime(start), DateUtil.formatDateTime(end));
                if (list != null && list.size() > 0) {
                    IotTslAttr attr = new IotTslAttr();
                    IotEquipment equipment = equipmentService.getEquipmentByCode(device);
                    if (equipment != null) {
                        List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                        if (attrs != null && attrs.size() > 0) {
                            for (IotTslAttr temp : attrs) {
                                if (temp.getAttrCode().equalsIgnoreCase(tag)) {
                                    attr = temp;
                                    break;
                                }
                            }
                        }
                    }
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (HistoryData data : list) {
                            if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
                                data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
                            }
                        }
                    }
                }
                double begin = 0;
                double stop = 0;
                for (int j = list.size() - 1; j >= 0; j--) {
                    final HistoryData historyData = list.get(j);
                    if (!"null".equals(historyData.getVal())) {
//                        doubleList.add(Double.parseDouble(historyData.getVal().toString()));
                        stop = Double.parseDouble(historyData.getVal().toString());
                        break;
                    }
                }
                for (int j = 0; j < list.size(); j++) {
                    final HistoryData historyData = list.get(j);
                    System.out.println(historyData.getVal().toString());
                    if (!"null".equals(historyData.getVal())) {
//                        doubleList.add(Double.parseDouble(historyData.getVal().toString()));
                        begin = Double.parseDouble(historyData.getVal().toString());
                        break;
                    }
                }
                ans.put(DateUtil.format(start, "yyyy/MM/dd"), (int) (stop - begin));
            } catch (IoTDBConnectionException ex) {
                ex.printStackTrace();
            } catch (StatementExecutionException ex) {
                ex.printStackTrace();
            }
        }
        return AjaxResult.success(ans);
//        return AjaxResult.error();

    }

    private JSONArray getTime(String device) {
        Date s, e;
        s = DateUtil.beginOfDay(new Date());
        e = new Date();
        JSONArray list = null;
        try {
            JSONObject result = new JSONObject();
            IotEquipment equipment = equipmentService.getEquipmentByCode(device);
            if (equipment != null) {
                List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                result.put("attrs", attrs);
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                list = ioTDBUtil.listDataHistoryV2(TENANT, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject temp = list.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        list.set(i, temp);
                                        break;
                                    } catch (Exception e1) {

                                    }

                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return list;
    }

    private long getEnergy(Date s, Date e) {
        String device = "100001";

        try {
            IotEquipment equipment = equipmentService.getEquipmentByCode(device);//总电表
            if (equipment != null) {
                List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
//                result.put("attrs", attrs);
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                JSONArray list1 = ioTDBUtil.listDataHistoryV2(TENANT, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list1.size(); i++) {
                            JSONObject temp = list1.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        if (tag != null) {
                                            final BigDecimal round = NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2);
                                        }
                                        list1.set(i, temp);
                                        break;
                                    } catch (Exception e1) {

                                    }
                                }
                            }
                        }
                    }
                }
//                result.put("list1", list1);
                long startEnergy = 0L;
                long endEnergy = 0L;
                for (int i = 0; i < list1.size(); i++) {
                    final Object o = list1.get(i);
                    final JSONObject jsonObject = JSONObject.parseObject(o.toString());
                    final Object totalEnergy = jsonObject.get("TotalEnergy");
                    if (totalEnergy != null) {
                        try {
                            startEnergy = Convert.toLong(totalEnergy);
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                        break;
                    }
                }
                for (int i = list1.size() - 1; i >= 0; i--) {
                    final Object o = list1.get(i);
                    final JSONObject jsonObject = JSONObject.parseObject(o.toString());
                    final Object totalEnergy = jsonObject.get("TotalEnergy");
                    if (totalEnergy != null) {
                        try {
                            endEnergy = Convert.toLong(totalEnergy);
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                        break;
                    }
                }
                return (endEnergy - startEnergy);
            }
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return 0;
    }
}
