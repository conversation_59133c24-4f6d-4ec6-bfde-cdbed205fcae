package com.boyo.iot.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.StringUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.util.IoTDBUtil;
import lombok.Data;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 物联网设备管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Data
public class IotEquipmentServiceImpl extends ServiceImpl<IotEquipmentMapper, IotEquipment> implements IIotEquipmentService {
    private final IotEquipmentMapper iotEquipmentMapper;
    private final IoTDBUtil ioTDBUtil;

    /**
     * 查询物联网设备管理列表
     *
     * @param iotEquipment 物联网设备管理
     * @return IotEquipment 列表
     */
    @Override
    public List<IotEquipment> selectIotEquipmentList(IotEquipment iotEquipment) {
        return iotEquipmentMapper.selectIotEquipmentList(iotEquipment);
    }

    @Override
    public List<IotEquipment> selectIotEquipmentListOnly(IotEquipment iotEquipment) {
        return iotEquipmentMapper.selectIotEquipmentListOnly(iotEquipment);
    }

    @Override
    public List<IotEquipment> selectIotEquipmentListOptimized(IotEquipment iotEquipment) {
        return iotEquipmentMapper.selectIotEquipmentListOptimized(iotEquipment);
    }

    @Override
    public IotEquipment getEquipmentDetail(Integer id) {
        IotEquipment obj = iotEquipmentMapper.getEquipmentDetail(id);
        for (IotTslAttr attr:obj.getAttrList()) {
          try {
              if((attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer")) && StrUtil.isNotEmpty(attr.getLastVal())){
                  attr.setLastVal(Convert.toStr(NumberUtil.round(Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple(),2)));
              }
              if (("工作状态".equals(attr.getAttrName())||"绿灯".equals(attr.getAttrName()))&&attr.getLastUpdateTime()!=null) {
                  final Date lastUpdateTime = attr.getLastUpdateTime();
                  final long between = DateUtil.between(lastUpdateTime, new Date(), DateUnit.MINUTE);
                  if (between > 5L) {
                      attr.setLastVal("0");
                  }
              }
          }catch (Exception e) {
              // 忽略单个属性处理异常
          }
        }
        return obj;
    }

    @Override
    public IotEquipment getEquipmentByCode(String code) {
        QueryWrapper<IotEquipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_code",code);
        return iotEquipmentMapper.selectOne(queryWrapper);
    }
}
