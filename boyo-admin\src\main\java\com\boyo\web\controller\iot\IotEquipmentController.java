package com.boyo.web.controller.iot;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.system.service.IEnterpriseDepartmentService;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 物联网设备管理Controller
 *
 * <AUTHOR>
 */
@Api("物联网设备管理")
@RestController
@RequestMapping("/iot/equipment")
public class IotEquipmentController extends BaseController {
    private final IIotEquipmentService iotEquipmentService;
    private final IEnterpriseDepartmentService enterpriseDepartmentService;

    // 江油接口缓存
    private List<IotEquipment> jiangyouList = null;
    private long timeforJiangyou = 0;

    // 构造函数注入
    public IotEquipmentController(IIotEquipmentService iotEquipmentService,
                                 IEnterpriseDepartmentService enterpriseDepartmentService) {
        this.iotEquipmentService = iotEquipmentService;
        this.enterpriseDepartmentService = enterpriseDepartmentService;
    }


    /**
     * 查询物联网设备管理列表（优化版本）
     */
    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/list")
    public TableDataInfo list(IotEquipment iotEquipment) {
        startPage();
        List<IotEquipment> list = null;
        try {
            list = iotEquipmentService.selectIotEquipmentListOptimized(iotEquipment);
            // 使用公共的数据处理逻辑
            processEquipmentList(list);
        } catch (Exception e) {
            System.err.println("查询设备列表异常: " + e.getMessage());
            e.printStackTrace();
            // 如果优化查询失败，回退到原始查询
            try {
                list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
                processEquipmentListSafely(list);
            } catch (Exception fallbackException) {
                System.err.println("回退查询也失败: " + fallbackException.getMessage());
                fallbackException.printStackTrace();
                list = new ArrayList<>(); // 返回空列表而不是null
            }
        }
        return getDataTable(list != null ? list : new ArrayList<>());
    }

    /**
     * 判断是否需要处理数值
     */
    private boolean shouldProcessNumericValue(IotTslAttr attr) {
        if (attr == null || attr.getAttrType() == null || attr.getLastVal() == null) {
            return false;
        }
        return (attr.getAttrType().equalsIgnoreCase("Double") ||
                attr.getAttrType().equalsIgnoreCase("Integer")) &&
                StrUtil.isNotEmpty(attr.getLastVal()) &&
                attr.getAttrMultiple() != null;
    }

    /**
     * 处理数值计算
     */
    private void processNumericValue(IotTslAttr attr) {
        if (attr == null || attr.getLastVal() == null || attr.getAttrMultiple() == null) {
            return;
        }
        try {
            double value = Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple();
            attr.setLastVal(Convert.toStr(NumberUtil.round(value, 2)));
        } catch (Exception e) {
            // 如果转换失败，保持原值
            System.err.println("数值处理异常: " + attr.getAttrName() + ", 值: " + attr.getLastVal() + ", 错误: " + e.getMessage());
        }
    }

    /**
     * 判断是否需要处理工作状态
     */
    private boolean shouldProcessWorkStatus(IotTslAttr attr) {
        if (attr == null || attr.getAttrName() == null) {
            return false;
        }
        return ("工作状态".equals(attr.getAttrName()) || "绿灯".equals(attr.getAttrName()))
                && attr.getLastUpdateTime() != null;
    }

    /**
     * 处理工作状态
     */
    private void processWorkStatus(IotTslAttr attr, Date currentTime) {
        if (attr == null || attr.getLastUpdateTime() == null || currentTime == null) {
            return;
        }
        try {
            final Date lastUpdateTime = attr.getLastUpdateTime();
            final long between = DateUtil.between(lastUpdateTime, currentTime, DateUnit.MINUTE);
            if (between > 5L) {
                attr.setLastVal("0");
            }
        } catch (Exception e) {
            System.err.println("工作状态处理异常: " + attr.getAttrName() + ", 错误: " + e.getMessage());
        }
    }


    /**
     * 查询物联网设备管理列表  对江油公司提供（优化版本）
     */
    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/jiangyou/device/list")
    public TableDataInfo jiangyouList(IotEquipment iotEquipment) {
        PageHelper.startPage(1, 1000);
        List<IotEquipment> list = null;
        if(System.currentTimeMillis() - timeforJiangyou <= 1000 * 30){
            list = jiangyouList;
            return getDataTable(list);
        }

        try {
            if (iotEquipment.getEquipmentImg() == null) {
                list = iotEquipmentService.selectIotEquipmentListOptimized(iotEquipment);
                // 复用优化后的数据处理逻辑
                processEquipmentList(list);
            } else {
                list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
            }
        } catch (Exception e) {
            System.err.println("江油接口查询异常: " + e.getMessage());
            e.printStackTrace();
            // 回退到安全的查询方式
            try {
                if (iotEquipment.getEquipmentImg() == null) {
                    list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
                    processEquipmentListSafely(list);
                } else {
                    list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
                }
            } catch (Exception fallbackException) {
                System.err.println("江油接口回退查询也失败: " + fallbackException.getMessage());
                fallbackException.printStackTrace();
                list = new ArrayList<>();
            }
        }
        timeforJiangyou = System.currentTimeMillis();
        jiangyouList = list;
        return getDataTable(list != null ? list : new ArrayList<>());
    }

    /**
     * 处理设备列表数据（提取公共逻辑）
     */
    private void processEquipmentList(List<IotEquipment> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        Date currentTime = new Date();
        // 改为普通流处理，避免并行流的线程安全问题
        list.stream()
                .filter(equipment -> equipment != null) // 过滤空设备
                .forEach(equipment -> {
                    try {
                        List<IotTslAttr> attrList = equipment.getAttrList();
                        if (attrList != null && !attrList.isEmpty()) {
                            attrList.stream()
                                    .filter(attr -> attr != null) // 过滤空属性
                                    .forEach(attr -> {
                                        try {
                                            if (shouldProcessNumericValue(attr)) {
                                                processNumericValue(attr);
                                            }
                                            if (shouldProcessWorkStatus(attr)) {
                                                processWorkStatus(attr, currentTime);
                                            }
                                        } catch (Exception e) {
                                            // 记录单个属性处理异常，但不影响其他属性处理
                                            System.err.println("处理属性异常: " + (attr.getAttrName() != null ? attr.getAttrName() : "unknown") + ", 错误: " + e.getMessage());
                                        }
                                    });
                        }
                    } catch (Exception e) {
                        // 记录单个设备处理异常，但不影响其他设备处理
                        System.err.println("处理设备异常: " + (equipment.getEquipmentName() != null ? equipment.getEquipmentName() : "unknown") + ", 错误: " + e.getMessage());
                    }
                });
    }

    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/listOnly")
    public TableDataInfo listOnly(IotEquipment iotEquipment) {
        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();
        if (userAdmin != 1) {
            iotEquipment.setDeptId(SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
        }
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        return getDataTable(list);
    }

    /**
     * 获取物联网设备管理详细信息
     */
    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iotEquipmentService.getById(id));
    }

    /**
     * 新增物联网设备管理
     */
    @ApiOperation("新增物联网设备管理")
    @PostMapping
    public AjaxResult add(@RequestBody IotEquipment iotEquipment) {
        iotEquipment.setOpenId(IdUtil.fastSimpleUUID());
        return toBooleanAjax(iotEquipmentService.save(iotEquipment));
    }

    /**
     * 修改物联网设备管理
     */
    @ApiOperation("修改物联网设备管理")
    @PutMapping
    public AjaxResult edit(@RequestBody IotEquipment iotEquipment) {
        return toBooleanAjax(iotEquipmentService.updateById(iotEquipment));
    }

    /**
     * 删除物联网设备管理
     */
    @ApiOperation("删除物联网设备管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iotEquipmentService.removeByIds(Arrays.asList(ids)));
    }

    @ApiOperation("查询物联网设备数据详情")
    @GetMapping("/getEquipmentDetail")
    public AjaxResult getEquipmentDetail(Integer id) {
        return AjaxResult.success(iotEquipmentService.getEquipmentDetail(id));
    }

    /**
     * 安全的设备列表处理方法（用于回退场景）
     */
    private void processEquipmentListSafely(List<IotEquipment> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        Date currentTime = new Date();
        // 使用传统的for循环，更加安全
        for (int i = 0; i < list.size(); i++) {
            IotEquipment equipment = list.get(i);
            if (equipment == null) {
                continue;
            }

            try {
                List<IotTslAttr> attrList = equipment.getAttrList();
                if (attrList == null || attrList.isEmpty()) {
                    continue;
                }

                for (int j = 0; j < attrList.size(); j++) {
                    IotTslAttr attr = attrList.get(j);
                    if (attr == null) {
                        continue;
                    }

                    try {
                        // 处理数值计算
                        if (attr.getAttrType() != null && attr.getLastVal() != null && attr.getAttrMultiple() != null) {
                            if ((attr.getAttrType().equalsIgnoreCase("Double") || attr.getAttrType().equalsIgnoreCase("Integer"))
                                    && StrUtil.isNotEmpty(attr.getLastVal())) {
                                try {
                                    double value = Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple();
                                    attr.setLastVal(Convert.toStr(NumberUtil.round(value, 2)));
                                } catch (Exception e) {
                                    // 忽略转换异常
                                }
                            }
                        }

                        // 处理工作状态
                        if (attr.getAttrName() != null && attr.getLastUpdateTime() != null) {
                            if (("工作状态".equals(attr.getAttrName()) || "绿灯".equals(attr.getAttrName()))) {
                                try {
                                    final Date lastUpdateTime = attr.getLastUpdateTime();
                                    final long between = DateUtil.between(lastUpdateTime, currentTime, DateUnit.MINUTE);
                                    if (between > 5L) {
                                        attr.setLastVal("0");
                                    }
                                } catch (Exception e) {
                                    // 忽略时间计算异常
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 忽略单个属性处理异常
                    }
                }
            } catch (Exception e) {
                // 忽略单个设备处理异常
            }
        }
    }
}
