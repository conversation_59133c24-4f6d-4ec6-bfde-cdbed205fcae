package com.boyo.web.controller.iot;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.system.service.IEnterpriseDepartmentService;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 物联网设备管理Controller
 *
 * <AUTHOR>
 */
@Api("物联网设备管理")
@RestController
@RequestMapping("/iot/equipment")
@AllArgsConstructor
public class IotEquipmentController extends BaseController {
    private final IIotEquipmentService iotEquipmentService;

    private final IEnterpriseDepartmentService enterpriseDepartmentService;


    /**
     * 查询物联网设备管理列表（优化版本）
     */
    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/list")
    public TableDataInfo list(IotEquipment iotEquipment) {
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOptimized(iotEquipment);

        // 使用公共的数据处理逻辑
        processEquipmentList(list);
        return getDataTable(list);
    }

    /**
     * 判断是否需要处理数值
     */
    private boolean shouldProcessNumericValue(IotTslAttr attr) {
        return (attr.getAttrType().equalsIgnoreCase("Double") ||
                attr.getAttrType().equalsIgnoreCase("Integer")) &&
                StrUtil.isNotEmpty(attr.getLastVal()) &&
                attr.getAttrMultiple() != null;
    }

    /**
     * 处理数值计算
     */
    private void processNumericValue(IotTslAttr attr) {
        try {
            double value = Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple();
            attr.setLastVal(Convert.toStr(NumberUtil.round(value, 2)));
        } catch (NumberFormatException e) {
            // 如果转换失败，保持原值
        }
    }

    /**
     * 判断是否需要处理工作状态
     */
    private boolean shouldProcessWorkStatus(IotTslAttr attr) {
        return ("工作状态".equals(attr.getAttrName()) || "绿灯".equals(attr.getAttrName()))
                && attr.getLastUpdateTime() != null;
    }

    /**
     * 处理工作状态
     */
    private void processWorkStatus(IotTslAttr attr, Date currentTime) {
        final Date lastUpdateTime = attr.getLastUpdateTime();
        final long between = DateUtil.between(lastUpdateTime, currentTime, DateUnit.MINUTE);
        if (between > 5L) {
            attr.setLastVal("0");
        }
    }


    /**
     * 查询物联网设备管理列表  对江油公司提供（优化版本）
     */
    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/jiangyou/device/list")
    public TableDataInfo jiangyouList(IotEquipment iotEquipment) {
        PageHelper.startPage(1, 1000);
        List<IotEquipment> list;
        if (iotEquipment.getEquipmentImg() == null) {
            list = iotEquipmentService.selectIotEquipmentListOptimized(iotEquipment);
            // 复用优化后的数据处理逻辑
            processEquipmentList(list);
        } else {
            list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        }

        return getDataTable(list);
    }

    /**
     * 处理设备列表数据（提取公共逻辑）
     */
    private void processEquipmentList(List<IotEquipment> list) {
        if (list != null && !list.isEmpty()) {
            Date currentTime = new Date();
            list.parallelStream().forEach(equipment -> {
                List<IotTslAttr> attrList = equipment.getAttrList();
                if (attrList != null && !attrList.isEmpty()) {
                    attrList.forEach(attr -> {
                        if (shouldProcessNumericValue(attr)) {
                            processNumericValue(attr);
                        }
                        if (shouldProcessWorkStatus(attr)) {
                            processWorkStatus(attr, currentTime);
                        }
                    });
                }
            });
        }
    }

    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/listOnly")
    public TableDataInfo listOnly(IotEquipment iotEquipment) {
        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();
        if (userAdmin != 1) {
            iotEquipment.setDeptId(SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
        }
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        return getDataTable(list);
    }

    /**
     * 获取物联网设备管理详细信息
     */
    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iotEquipmentService.getById(id));
    }

    /**
     * 新增物联网设备管理
     */
    @ApiOperation("新增物联网设备管理")
    @PostMapping
    public AjaxResult add(@RequestBody IotEquipment iotEquipment) {
        iotEquipment.setOpenId(IdUtil.fastSimpleUUID());
        return toBooleanAjax(iotEquipmentService.save(iotEquipment));
    }

    /**
     * 修改物联网设备管理
     */
    @ApiOperation("修改物联网设备管理")
    @PutMapping
    public AjaxResult edit(@RequestBody IotEquipment iotEquipment) {
        return toBooleanAjax(iotEquipmentService.updateById(iotEquipment));
    }

    /**
     * 删除物联网设备管理
     */
    @ApiOperation("删除物联网设备管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iotEquipmentService.removeByIds(Arrays.asList(ids)));
    }

    @ApiOperation("查询物联网设备数据详情")
    @GetMapping("/getEquipmentDetail")
    public AjaxResult getEquipmentDetail(Integer id) {
        return AjaxResult.success(iotEquipmentService.getEquipmentDetail(id));
    }
}
