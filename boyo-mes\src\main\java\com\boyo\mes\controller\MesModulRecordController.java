package com.boyo.mes.controller;

import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.service.IMesModulRecordService;
import com.boyo.mes.vo.ModuleRecordVO;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * (MesModulRecord)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Api("")
@RestController
@RequestMapping("/mes/mesModulRecord")
@AllArgsConstructor
public class MesModulRecordController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesModulRecordService mesModulRecordService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(MesModulRecord mesModulRecord) {
        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();

        // 如果当前用户不是管理员，则设置部门ID为当前用户所属企业的部门OpenID
        if (userAdmin != 1) {
            mesModulRecord.setDeptId(SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
        }

        startPage();
        List<MesModulRecord> list = mesModulRecordService.selectMesModulRecordList(mesModulRecord);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getCreateUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    MesModulRecord action = list.get(i);
                    List<EnterpriseUser> temps = userList.stream().filter(s -> s.getId().equals(action.getCreateUserId())).collect(Collectors.toList());
                    list.get(i).setCreateUserName(temps.get(0).getUserFullName());
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesModulRecordService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody MesModulRecord mesModulRecord) {
        return toBooleanAjax(mesModulRecordService.save(mesModulRecord));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody MesModulRecord mesModulRecord) {
        return toBooleanAjax(mesModulRecordService.updateById(mesModulRecord));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesModulRecordService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取当前设备模具信息
     * @return
     */
    @GetMapping("/listCurrentModule")
    public AjaxResult listCurrentModule(){
        List<ModuleRecordVO> moduleRecordVOS = mesModulRecordService.listCurrentModule();
        return AjaxResult.success(moduleRecordVOS);
    }

    /**
     * 卸载模具
     * @param vo
     * @return
     */
    @PostMapping("/stopModule")
    public AjaxResult stopModule(@RequestBody ModuleRecordVO vo){
        mesModulRecordService.stopModule(vo);
        return AjaxResult.success();
    }

    /**
     * 换模
     * @param vo
     * @return
     */
    @PostMapping("/changeModule")
    public AjaxResult changeModule(@RequestBody ModuleRecordVO vo){
        mesModulRecordService.changeModule(vo);
        return AjaxResult.success();
    }
}
