package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.entity.IorealData;

import java.util.List;

/**
 * IoT实时数据(IorealData)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-31 17:07:23
 */
@Tenant
public interface IorealDataMapper extends BaseMapper<IorealData> {

    /**
     * 通过实体作为筛选条件查询
     *
     * @param iorealData 实例对象
     * @return 对象列表
     */
    List<IorealData> selectIorealDataList(IorealData iorealData);

    void saveOrUpdate(List<IorealData> list);

    /**
     * 批量更新优化方法（适用于更新频繁的场景）
     */
    void batchUpdateOptimized(List<IorealData> list);

    /**
     * 批量插入新记录
     */
    void batchInsertNew(List<IorealData> list);

}

