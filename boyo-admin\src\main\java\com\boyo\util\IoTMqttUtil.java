package com.boyo.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.mapper.IorealDataMapper;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.service.IIotEquipmentPropService;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.processor.TenantProcessor;
import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.service.ITSysEnterpriseService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class IoTMqttUtil implements MqttCallback {
    @Autowired
    private ITSysEnterpriseService sysEnterpriseService;
    @Autowired
    private IorealDataMapper iorealDataMapper;
    @Autowired
    private TenantProcessor tenantProcessor;
    @Autowired
    private IoTDBUtil ioTDBUtil;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IIotEquipmentPropService equipmentPropService;
    @Value("${mqtt.url}")
    public String HOST;
    @Value("${mqtt.topic}")
    public String TOPIC;
    @Value("${mqtt.username}")
    private String name;
    @Value("${mqtt.password}")
    private String passWord;

    private MqttClient client;
    private MqttConnectOptions options;
    String clientid = String.valueOf(System.currentTimeMillis());

    @PostConstruct
    public void connect() {
        try {
            // host为主机名，clientid即连接MQTT的客户端ID，一般以唯一标识符表示，MemoryPersistence设置clientid的保存形式，默认为以内存保存
            client = new MqttClient(HOST, clientid, new MemoryPersistence());
            // MQTT的连接设置
            options = new MqttConnectOptions();
            // 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，这里设置为true表示每次连接到服务器都以新的身份连接
            options.setCleanSession(false);
            // 设置连接的用户名
            options.setUserName(name);
            // 设置连接的密码
            options.setPassword(passWord.toCharArray());
            // 设置超时时间 单位为秒
            options.setConnectionTimeout(10);
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            options.setKeepAliveInterval(3600);
            // 设置回调
            client.setCallback(this);
            client.connect(options);
            //订阅消息
            int[] Qos = {1};
            String[] topic1 = new String[]{"hsl/#"};
            client.subscribe(topic1, Qos);
            log.info("连接成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("ReportMqtt客户端连接异常，异常信息：" + e);
        }

    }

    @Override
    public void connectionLost(Throwable throwable) {
        try {
            throwable.printStackTrace();
            log.info("程序出现异常，DReportMqtt断线！正在重新连接...:");
            client.close();
            this.connect();
            log.info("ReportMqtt重新连接成功");
        } catch (MqttException e) {
            log.info(e.getMessage());
        }
    }

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        // 在线程池中执行代码
        executorService.execute(() -> {
            try {
                if (topic.startsWith("hsl")) {
                    executeHsl(topic, message);
                } else if (topic.startsWith("room")) {
                    executeRoom(topic, message);
                } else if (topic.startsWith("mengdou")) {
                    executeMengdou(topic, message);
                } else if (topic.startsWith("cnc")) {
                    executeCNC(topic, message);
                } else if (topic.startsWith("ali")) {
                    executeAli(topic, message);
                } else if (topic.startsWith("qimiyi")) {
                    executeQimiyi(topic, message);
                } else if (topic.startsWith("transpond")) {
                    executeLemonTranspond(topic, message);
                } else if (topic.startsWith("ningmeng")) {
                    executeLemon(topic, message);
                } else if (topic.startsWith("mdio")) {
                    executeMdIO(topic, message);
                } else if (topic.startsWith("mdplc")) {
                    executeMdPlc(topic, message);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void executeCNC(String topic, MqttMessage message) {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = "";
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            //          手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);
            try {
                DataProcessResult result = new DataProcessResult();

                JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                for (Map.Entry<String, Object> entry : msg.entrySet()) {
                    deviceCode = entry.getKey().trim();
                    break; // 只取第一个设备编码
                }

                JSONObject data = msg.getJSONArray(deviceCode).getJSONObject(0).getJSONObject("values");

                // 处理网关缓存
                String sn = data.getString("ts");
                String cacheKey = enterpriseCode + ":" + deviceCode;


                // 处理特定的CNC数据转换
                if (data.containsKey("Count_count")) {
                    data.put("Number", data.get("Count_count"));
                }

                if (data.containsKey("CNCStatus_cncStatus")) {
                    String cncStatus = data.getString("CNCStatus_cncStatus");
                    if (cncStatus.contains("MANUAL")) {
                        data.put("Work_Status", "2");
                        data.put("Product_Status", "1");
                        data.put("Power_ON", "2"); //待机
                    } else if (cncStatus.contains("AUTO")) {
                        data.put("Work_Status", "1");
                        data.put("Product_Status", "1");
                        data.put("Power_ON", "1"); //开机
                    } else if (cncStatus.contains("OFF")) {
                        data.put("Work_Status", "0");
                        data.put("Product_Status", "0");
                        data.put("Power_ON", "0"); //关机
                    } else {
                        data.put("Work_Status", "0");
                        data.put("Product_Status", "0");
                        data.put("Power_ON", "0"); //关机
                    }
                }
                List<IorealData> realList = new ArrayList<>();
                List<IorealData> tempList = new ArrayList<>();
                StringBuffer str = new StringBuffer("");
                // 处理数据
                for (Map.Entry entry : data.entrySet()) {
                    IorealData obj = new IorealData();
                    obj.setTag(Convert.toStr(entry.getKey()));
                    obj.setDeviceCode(deviceCode);
                    obj.setVal(Convert.toStr(entry.getValue()));
                    obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp)) {
                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                            realList.add(obj);
                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                        }
                    } else {
                        realList.add(obj);
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(obj);
                }
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                }

            } catch (Exception e) {
                log.error("CNC数据处理异常：{}", e.getMessage());
                e.printStackTrace();
            }

        }
    }

    private void executeLemon(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
//        System.out.println(new String(message.getPayload()));
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg.containsKey("Body")) {
                        String entityNo = msg.getString("EntityNo");
                        JSONArray array = msg.getJSONArray("Body");
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                List<IorealData> realList = new ArrayList<>();
                                List<IorealData> tempList = new ArrayList<>();
                                StringBuffer str = new StringBuffer("");
                                JSONObject object = array.getJSONObject(i);
                                String deviceCode = entityNo + "_" + object.getString("DeviceId");
                                for (Map.Entry entry : object.entrySet()) {
                                    if (entry.getKey().equals("DeviceId")) {
                                        continue;
                                    }
                                    IorealData obj = new IorealData();
                                    obj.setTag(Convert.toStr(entry.getKey()));
                                    obj.setDeviceCode(deviceCode);
                                    obj.setVal(Convert.toStr(entry.getValue()));
                                    obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                                    str.setLength(0);
                                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
                                    Object temp = redisCache.getCacheObject(str.toString());
                                    if (ObjectUtil.isNotNull(temp)) {
                                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                        }
                                    } else {
                                        realList.add(obj);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                    }
                                    tempList.add(obj);
                                }
                                if (tempList.size() > 0) {
                                    iorealDataMapper.saveOrUpdate(tempList);
                                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                                }
                                if (realList.size() > 0) {
                                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeLemonTranspond(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg.containsKey("Body")) {
                        String entityNo = msg.getString("DeviceNo");
                        JSONArray array = msg.getJSONArray("Body");
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                List<IorealData> realList = new ArrayList<>();
                                List<IorealData> tempList = new ArrayList<>();
                                StringBuffer str = new StringBuffer("");
                                JSONObject object = array.getJSONObject(i);
                                String deviceCode = entityNo + "_" + object.getString("Module");
                                for (Map.Entry entry : object.entrySet()) {
                                    IorealData obj = new IorealData();
                                    obj.setTag(Convert.toStr(entry.getKey()));
                                    obj.setDeviceCode(deviceCode);
                                    obj.setVal(Convert.toStr(entry.getValue()));
                                    obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                                    str.setLength(0);
                                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
                                    Object temp = redisCache.getCacheObject(str.toString());
                                    if (ObjectUtil.isNotNull(temp)) {
                                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                        }
                                    } else {
                                        realList.add(obj);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                    }
                                    tempList.add(obj);
                                }
                                if (tempList.size() > 0) {
                                    iorealDataMapper.saveOrUpdate(tempList);
                                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                                }
                                if (realList.size() > 0) {
                                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }
    /**
     * 数据处理结果
     */
    public static class DataProcessResult {
        private List<IorealData> tempList;
        private List<IorealData> realList;

        public DataProcessResult() {
            this.tempList = new ArrayList<>();
            this.realList = new ArrayList<>();
        }

        public List<IorealData> getTempList() {
            return tempList;
        }

        public List<IorealData> getRealList() {
            return realList;
        }
    }
    private void executeRoom(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    JSONArray array = msg.getJSONArray("params");
                    if (array != null && array.size() > 0) {
                        for (int i = 0; i < array.size(); i++) {
                            List<IorealData> realList = new ArrayList<>();
                            List<IorealData> tempList = new ArrayList<>();
                            StringBuffer str = new StringBuffer("");
                            JSONObject object = array.getJSONObject(i);
                            JSONArray propertiesArray = object.getJSONArray("properties");
                            String deviceCode = array.getJSONObject(i).getString("clientID");
                            for (int i1 = 0; i1 < propertiesArray.size(); i1++) {
                                propertiesArray.getJSONObject(i1).getString("name");
                                IorealData obj = new IorealData();
                                obj.setTag(Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")));
                                obj.setDeviceCode(deviceCode);
                                obj.setVal(Convert.toStr(propertiesArray.getJSONObject(i1).getString("value")));
                                obj.setKey(deviceCode + "-" + Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")));
                                str.setLength(0);
                                str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")));
                                Object temp = redisCache.getCacheObject(str.toString());
                                if (ObjectUtil.isNotNull(temp)) {
                                    if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                        realList.add(obj);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")), 60, TimeUnit.SECONDS);
                                    }
                                } else {
                                    realList.add(obj);
                                    redisCache.setCacheObject(str.toString(), Convert.toStr(propertiesArray.getJSONObject(i1).getString("value")), 60, TimeUnit.SECONDS);
                                }
                                tempList.add(obj);
                            }
                            if (tempList.size() > 0) {
                                iorealDataMapper.saveOrUpdate(tempList);
                                equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                            }
                            if (realList.size() > 0) {
                                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeMengdou(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
//        System.out.println(topic);
//        System.out.println(new String(message.getPayload()));
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg.containsKey("Body")) {
                        String entityNo = msg.getString("GwId");
                        JSONArray array = msg.getJSONArray("Body");
//                        System.out.println(array.toJSONString());
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                List<IorealData> realList = new ArrayList<>();
                                List<IorealData> tempList = new ArrayList<>();
                                StringBuffer str = new StringBuffer("");
                                JSONObject object = array.getJSONObject(i);
                                String deviceCode = entityNo + "_" + object.getString("Module");
                                //System.out.println(object);
                                JSONObject vals = object.getJSONObject("Reg_Val");
                                if (vals != null) {
                                    for (Map.Entry entry : vals.entrySet()) {
                                        IorealData obj = new IorealData();
                                        obj.setTag(Convert.toStr(entry.getKey()));
                                        obj.setDeviceCode(deviceCode);
                                        obj.setVal(Convert.toStr(entry.getValue()));
                                        obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                                        str.setLength(0);
                                        str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
//                                    System.out.println(str.toString());
                                        Object temp = redisCache.getCacheObject(str.toString());
                                        if (ObjectUtil.isNotNull(temp)) {
                                            if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                                realList.add(obj);
                                                redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                            }
                                        } else {
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                        }
                                        tempList.add(obj);
                                    }
                                }

                                if (tempList.size() > 0) {
                                    iorealDataMapper.saveOrUpdate(tempList);
                                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                                }
//                                System.out.println(JSONObject.toJSONString(realList));
                                if (realList.size() > 0) {
//                                    System.out.println(JSONObject.toJSONString(realList));
                                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeAli(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = topics[2];
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            //          手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);
            try {
                JSONObject msg = JSONObject.parseObject(new String(message.getPayload())).getJSONObject("params");
                List<IorealData> tempList = new ArrayList<>();
//                msg.remove("id");
//                msg.remove("dtime");
                if (msg.size() == 0) {
                    return;
                }
//                System.out.println(msg);
                List<IorealData> realList = new ArrayList<>();
                StringBuffer str = new StringBuffer("");
                for (Map.Entry<String, Object> entry : msg.entrySet()) {
                    IorealData obj = new IorealData();
                    String key = entry.getKey().replace("！", "").replace("!", "").trim();
                    obj.setTag(key);
                    obj.setDeviceCode(deviceCode);
                    obj.setVal(Convert.toStr(entry.getValue()));
                    obj.setKey(deviceCode + "-" + entry.getKey());
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(entry.getKey());
//                    System.out.println(obj);
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp) && !entry.getKey().contains("OEE")) {
                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                            realList.add(obj);
                        }
                    } else {
                        realList.add(obj);
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(obj);
                }
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 执行MdIO操作
     * <p>
     * 该方法负责处理MQTT消息，解析消息内容，并根据企业代码切换数据源
     * 它还负责将解析后的数据保存到数据库和缓存中，并调用相关服务处理设备属性
     *
     * @param topic   MQTT消息的主题，用于获取企业代码
     * @param message MQTT消息对象，包含实际的数据
     */
    private void executeMdIO(String topic, MqttMessage message) {
        // 分割主题以获取企业代码
        String[] topics = topic.split("/", 2);
        if (topics.length < 2) {
            return;
        }
        String enterpriseCode = topics[1];
        String deviceCode = "";

        // 检查企业代码和数据源是否有效
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            // 手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);

            try {
                // 初始化临时列表和字符串缓冲区
                List<IorealData> tempList = new ArrayList<>();
                List<IorealData> realList = new ArrayList<>();
                StringBuffer str = new StringBuffer("");
                // 解析消息内容为JSON数组
                final JSONArray objects = JSONArray.parseArray(message.toString());

                // 遍历JSON数组中的每个对象
                for (int i = 0; i < objects.size(); i++) {
                    JSONObject msg = objects.getJSONObject(i);
                    deviceCode = msg.getString("devName");

                    JSONObject data = new JSONObject();

                    // 尝试获取和解析不同的数据字段
                    try {
                        JSONArray diState = msg.getJSONArray("diState");
                        data.put("Power_ON", diState.getString(1));

                        JSONArray counter = msg.getJSONArray("counter");
                        data.put("Number", counter.getString(0));

                        JSONArray cycle = msg.getJSONArray("cycle");
                        data.put("Cycle", cycle.getString(0));
                    } catch (Exception e) {
                        // 如果解析失败，忽略错误
                    }

                    // 遍历解析后的数据，创建IorealData对象，并进行处理
                    for (Map.Entry<String, Object> entry : data.entrySet()) {
                        IorealData iorealData = new IorealData();
                        String key = entry.getKey().replace("！", "").replace("!", "").trim();
                        iorealData.setTag(key);
                        iorealData.setDeviceCode(deviceCode);
                        iorealData.setVal(Convert.toStr(entry.getValue()));
                        iorealData.setKey(deviceCode + "-" + key);

                        // 构建缓存键
                        str.setLength(0);
                        str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(key);

                        // 检查缓存中是否存在该键的值
                        Object temp = redisCache.getCacheObject(str.toString());
                        if (ObjectUtil.isNotNull(temp) && !key.contains("OEE")) {
                            // 如果缓存中的值与当前值不同，则添加到realList中
                            if (!Convert.toStr(temp).equals(iorealData.getVal())) {
                                realList.add(iorealData);
                            }
                        } else {
                            realList.add(iorealData);
                            // 将当前值添加到缓存中
                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                        }
                        tempList.add(iorealData);
                    }
                }

                // 如果tempList不为空，保存或更新数据，并处理设备故障属性
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }

                // 如果realList不为空，将数据添加到IoT数据库中
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 执行MDPLC消息处理
     * 该方法负责解析MQTT消息，并根据主题和消息内容执行相应的处理逻辑
     *
     * @param topic   消息主题，用于确定企业代码
     * @param message MQTT消息，包含PLC数据
     */
    private void executeMdPlc(String topic, MqttMessage message) {
        // 分割主题以获取企业代码
        String[] topics = topic.split("/", 2);
        if (topics.length < 2) {
            return;
        }
        String enterpriseCode = topics[1];
        String deviceCode = "";

        // 检查企业代码和数据源是否有效
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            // 手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);

            try {
                // 初始化临时列表和字符串缓冲区
                List<IorealData> tempList = new ArrayList<>();
                List<IorealData> realList = new ArrayList<>();
                StringBuffer str = new StringBuffer("");
                // 解析消息内容为JSON数组
                final JSONObject objects = JSONObject.parseObject(message.toString());
                try {
                    deviceCode = objects.getString("projectName") + "_" + objects.getString("plcName");
                } catch (Exception e) {
                    e.printStackTrace();
                    return;
                }
                JSONArray dataArray = objects.getJSONArray("datas");
                if (dataArray == null) {
                    return;
                }
                JSONObject data = new JSONObject();
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject msg = dataArray.getJSONObject(i);
                    try {
                        data.put(msg.getString("name"), msg.getString("value"));
                    } catch (Exception e) {
                        continue;
                    }
                }

                // 遍历解析后的数据，创建IorealData对象，并进行处理
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    IorealData iorealData = new IorealData();
                    String key = entry.getKey().replace("！", "").replace("!", "").trim();
                    iorealData.setTag(key);
                    iorealData.setDeviceCode(deviceCode);
                    iorealData.setVal(Convert.toStr(entry.getValue()));
                    iorealData.setKey(deviceCode + "-" + key);
                    // 构建缓存键
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(key);

                    // 检查缓存中是否存在该键的值
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp) && !key.contains("OEE")) {
                        // 如果缓存中的值与当前值不同，则添加到realList中
                        if (!Convert.toStr(temp).equals(iorealData.getVal())) {
                            realList.add(iorealData);
                        }
                    } else {
                        realList.add(iorealData);
                        // 将当前值添加到缓存中
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(iorealData);
                }
                // 如果tempList不为空，保存或更新数据，并处理设备故障属性
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                // 如果realList不为空，将数据添加到IoT数据库中
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void executeQimiyi(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = topics[2];
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            //          手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);
            try {
                JSONObject msg = JSONObject.parseObject(new String(message.getPayload())).getJSONObject("params");
                List<IorealData> tempList = new ArrayList<>();
                if (msg.size() == 0) {
                    return;
                }
                StringBuffer str = new StringBuffer("");
                str.append(enterpriseCode).append("-").append(deviceCode).append("-").append(msg.getString("id"));
                Object exist = redisCache.getCacheObject(str.toString());
                if (ObjectUtil.isNotNull(exist)) {
                    return;
                }
                redisCache.setCacheObject(str.toString(), "1", 20, TimeUnit.DAYS);
//                System.out.println(msg);
                List<IorealData> realList = new ArrayList<>();
                str.setLength(0);
                for (Map.Entry<String, Object> entry : msg.entrySet()) {
                    IorealData obj = new IorealData();
                    obj.setTag(entry.getKey());
                    obj.setDeviceCode(deviceCode);
                    obj.setVal("'" + Convert.toStr(entry.getValue()).replaceAll("[\t\n\r]", "") + "'");
                    obj.setKey(deviceCode + "-" + entry.getKey());
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(entry.getKey());
//                    System.out.println(obj);
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp) && !entry.getKey().contains("OEE")) {
                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                            realList.add(obj);
                        }
                    } else {
                        realList.add(obj);
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(obj);
                }
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        //log.info("消息发送成功");
    }

    private boolean checkEnterprise(String enterpriseCode) {
        if (existList.containsKey(enterpriseCode)) {
            return true;
        } else {
            if (errorList.containsKey(enterpriseCode) && Convert.toInt(errorList.get(enterpriseCode)) < 100) {
//                重新获取企业信息
                errorList.put(enterpriseCode, Convert.toStr(Convert.toInt(errorList.get(enterpriseCode)) + 1));
                return false;
            } else {
                DynamicDataSourceContextHolder.push("master");
                QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("enterprise_openid", enterpriseCode);
                TSysEnterprise enterprise = sysEnterpriseService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(enterprise) && enterprise.getEnterpriseInit().equals("1")) {
                    existList.put(enterpriseCode, "1");
                    return true;
                } else {
                    errorList.put(enterpriseCode, "1");
                    return false;
                }
            }
        }
    }

    LinkedHashMap<String, String> existList = new LinkedHashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 200;
        }
    };

    LinkedHashMap<String, String> errorList = new LinkedHashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 200;
        }
    };

    private void executeHsl(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        if (topics.length == 3) {
            String enterpriseCode = topics[1];
            String deviceCode = topics[2];

//            System.out.println("接收到topic:"+topic);
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                List<IorealData> realList = new ArrayList<>();
                List<IorealData> tempList = new ArrayList<>();
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg != null) {
                        if (msg.containsKey("__failedMsg")) {
                            String failedMsg = msg.getString("__failedMsg");
                            if (failedMsg.contains("Exception")|| failedMsg.contains("失败")) {
                                log.info("异常数据，不做处理------=====-----");
                                return;
                            }
                        }
                        //手动切换数据源
                        DynamicDataSourceContextHolder.push(enterpriseCode);
//                        System.out.println("手动切换数据源:"+enterpriseCode);

                        StringBuffer str = new StringBuffer("");
                        for (Map.Entry<String, Object> entry : msg.entrySet()) {
                            if (entry.getKey().startsWith("__")) {
                                continue;
                            } else {
                                IorealData iorealData = new IorealData();
                                iorealData.setDeviceCode(deviceCode);
                                String key = entry.getKey().replace("！", "").replace("!", "").trim();
                                iorealData.setTag(key);
                                String tempVal=Convert.toStr(entry.getValue());
                                if("0.0".equals(tempVal)){
                                    tempVal="0";
                                }else if("1.0".equals(tempVal)){
                                    tempVal="1";
                                }
                                iorealData.setVal(tempVal);
                                iorealData.setUpdateTime(new Date());
                                iorealData.setKey(deviceCode + "-" + entry.getKey());

                                if (msg.containsKey("__activeTime")) {
                                    try {
                                        iorealData.setUpdateTime(DateUtil.parse(Convert.toStr(msg.get("__activeTime"))));
                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }
                                }

                                str.setLength(0);
                                str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
                                Object temp = redisCache.getCacheObject(str.toString());
                                if (ObjectUtil.isNotNull(temp)) {
                                    if (!Convert.toStr(temp).equals(iorealData.getVal())) {
//                                        数据发生变化
                                        realList.add(iorealData);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                    }
                                } else {
                                    realList.add(iorealData);
                                    redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                }
                                tempList.add(iorealData);
                            }
                        }
                        if (tempList.size() > 0) {
                            iorealDataMapper.saveOrUpdate(tempList);
                            equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                        }
                        if (realList.size() > 0) {
//                                    System.out.println(JSONObject.toJSONString(realList));
                            ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }
}
