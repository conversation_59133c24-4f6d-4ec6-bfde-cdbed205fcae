## 数据源配置
spring:
    datasource:
        # 非多租户项目使用默认数据源配置
        url: ${SPRING_DATASOURCE_URL:******************************************************************************************************************************************************************************************************}
        username: ${SPRING_DATASOURCE_USERNAME:root}
        password: ${SPRING_DATASOURCE_PASSWORD:fCda13XJbz3eDEEH}
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 多租户项目使用动态数据源配置
        dynamic:
            druid:
                wall:
                    multiStatementAllow: true
                    noneBaseStatementAllow: true
                fail-fast: true
                break-after-acquire-failure: true
                # 初始连接数
                initialSize: 5
                # 最小连接池数量
                minIdle: 5
                # 最大连接池数量
                maxActive: 20
                # 配置获取连接等待超时的时间
                maxWait: 6000
                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                timeBetweenEvictionRunsMillis: 60000
                # 配置一个连接在池中最小生存的时间，单位是毫秒
                minEvictableIdleTimeMillis: 300000
                # 配置一个连接在池中最大生存的时间，单位是毫秒
                maxEvictableIdleTimeMillis: 900000
                filter:
                    stat:
                        enabled: true
                        # 慢SQL记录
                        log-slow-sql: true
                        slow-sql-millis: 1000
                        merge-sql: true

            strict: true
            seata: true
            datasource:
                master:
                    url: ${SPRING_DATASOURCE_URL:******************************************************************************************************************************************************************************************************}
                    username: ${SPRING_DATASOURCE_USERNAME:root}
                    password: ${SPRING_DATASOURCE_PASSWORD:fCda13XJbz3eDEEH}
                    driver-class-name: com.mysql.cj.jdbc.Driver
# 数据源配置
#spring:
#    datasource:
#        # 非多租户项目使用默认数据源配置
#        url: ${SPRING_DATASOURCE_URL:*************************************************************************************************************************************************************************************************************************************************}
#        username: ${SPRING_DATASOURCE_USERNAME:boyo}
#        password: ${SPRING_DATASOURCE_PASSWORD:Boyo12345}
#        driver-class-name: com.mysql.cj.jdbc.Driver
#        # 多租户项目使用动态数据源配置
#        dynamic:
#            druid:
#                wall:
#                    multiStatementAllow: true
#                    noneBaseStatementAllow: true
#                fail-fast: true
#                break-after-acquire-failure: true
#                # 初始连接数
#                initialSize: 5
#                # 最小连接池数量
#                minIdle: 10
#                # 最大连接池数量
#                maxActive: 20
#                # 配置获取连接等待超时的时间
#                maxWait: 60000
#                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#                timeBetweenEvictionRunsMillis: 60000
#                # 配置一个连接在池中最小生存的时间，单位是毫秒
#                minEvictableIdleTimeMillis: 300000
#                # 配置一个连接在池中最大生存的时间，单位是毫秒
#                maxEvictableIdleTimeMillis: 900000
#                # 配置检测连接是否有效
#                validationQuery: SELECT 1 FROM DUAL
#                filter:
#                    stat:
#                        enabled: true
#                        # 慢SQL记录
#                        log-slow-sql: true
#                        slow-sql-millis: 1000
#                        merge-sql: true
#
#            strict: true
#            seata: true
#            datasource:
#                master:
#                    url: ${SPRING_DATASOURCE_URL:*************************************************************************************************************************************************************************************************************************************************}
#                    username: ${SPRING_DATASOURCE_USERNAME:boyo}
#                    password: ${SPRING_DATASOURCE_PASSWORD:Boyo12345}
#                    driver-class-name: com.mysql.cj.jdbc.Driver
