package com.boyo.web.controller.dashang;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTsl;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.service.IIotTslService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.master.domain.ScreenWarehousing;
import com.boyo.master.domain.ScreenWorkshop;
import com.boyo.master.domain.ScreenYield;
import com.boyo.master.service.IScreenWarehousingService;
import com.boyo.master.service.IScreenWorkshopService;
import com.boyo.master.service.IScreenYieldService;
import com.boyo.web.controller.iot.IotPorductionController;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dashang/screen")
public class DaShangScreenController {

    @Resource
    private IIotEquipmentService iotEquipmentService;
    @Resource
    private IIotTslService iotTslService;
    @Resource
    private IoTDBUtil ioTDBUtil;
    @Resource
    private IotPorductionController iotPorductionController;

    @Autowired
    private IScreenYieldService screenYieldService;
    @Autowired
    IScreenWorkshopService screenWorkshopService;

    @Autowired
    private IScreenWarehousingService screenWarehousingService;

    /*
     * 重点数据模块查询接口
     * */
    @GetMapping("/important/data")
    public AjaxResult importantData(ScreenWorkshop screenWorkshop) {
        List<IotEquipment> iotEquipments = iotEquipmentService.selectIotEquipmentList(new IotEquipment());
        int totalCount = 0;
        int onlineCount = 0;
        int workCount = 0;
        for (IotEquipment iotEquipment : iotEquipments) {
            IotEquipment equipmentDetail = iotEquipmentService.getEquipmentDetail(Integer.parseInt(iotEquipment.getId() + ""));
            List<IotTslAttr> attrList = equipmentDetail.getAttrList();
            if (equipmentDetail.getTslId() == 9) {
                continue;
            }
            totalCount++;
            if (attrList != null && attrList.size() > 0) {
                if (attrList.get(0).getLastUpdateTime() == null) {
                    continue;
                }
                long between = DateUtil.between(attrList.get(0).getLastUpdateTime(), DateTime.now(), DateUnit.MINUTE);
                if (between < 10L) {
                    onlineCount++;
                }
            }

            if (attrList != null && attrList.size() >= 4) {
                if ("生产状态".equals(attrList.get(3).getAttrName()))
                    if ("1".equals(attrList.get(3).getLastVal())) {
                        workCount++;
                    }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("onlineRate", onlineCount * 100 / totalCount + "%");
        jsonObject.put("workRate", workCount * 100 / totalCount + "%");
        jsonObject.put("alarmRate", "0%");
        return AjaxResult.success(jsonObject);
    }


    /**
     * 获取设备数据
     */
    @GetMapping("/equipment/data")
    private AjaxResult getEquipmentData() throws StatementExecutionException, IoTDBConnectionException {
        // 存储所有设备数据的列表
        List<JSONObject> ansList = new ArrayList<>();
        // 获取所有设备类型列表
        List<IotTsl> list = iotTslService.selectIotTslList(new IotTsl());
        for (IotTsl iotTsl : list) {
            // 跳过电表管理
            if (iotTsl.getId() == 9) {
                continue;
            }
            IotEquipment iotEquipment = new IotEquipment();
            iotEquipment.setTslId(iotTsl.getId());
            // 查询该设备类型下的所有具体设备
            List<IotEquipment> iotEquipments = iotEquipmentService.selectIotEquipmentList(iotEquipment);

            // 构建单个设备类型的统计信息
            JSONObject one = new JSONObject();
            one.put("name", iotTsl.getTslName()); // 设备类型名称
            one.put("totalCount", iotEquipments.size()); // 该设备类型的总设备数量
            int onlineCount = 0; //在线设备数量
            int yieldCount = 0; //产量数量
            float onlineTime = 0L;
            float totalTime = 0L;
            float workingTime = 0L;

            // 遍历每个具体设备，获取设备的实时状态和产量数据
            for (IotEquipment equipment : iotEquipments) {
                // 获取设备的属性列表
                List<IotTslAttr> attrList = equipment.getAttrList();
                // 遍历设备的每个属性，寻找特定的属性（如开关机状态和产量）
                for (IotTslAttr attr : attrList) {
                    if ("开关机状态".equals(attr.getAttrName())) {
                        // 如果设备的开关机状态为开，则在线设备数量增加
                        if ("1".equals(attr.getLastVal())) {
                            onlineCount++;
                        }
                        break;
                    }
//                    else if ("产量".equals(attr.getAttrName())) {
//                        // 如果设备的产量属性不为空，则累加到总产量
//                        if (attr.getLastVal() != null) {
//                            yieldCount += Integer.parseInt(attr.getLastVal());
//                        }
//                    }
                }
                // 获取数据的方法调用，此处省略具体实现
                JSONArray data = getData("4b880be617ba45629033ae5af382b6aa", equipment.getEquipmentCode(),
                        DateUtil.formatDateTime(DateUtil.beginOfDay(new Date())),
                        DateUtil.formatDateTime(new Date()));
                if (data != null && data.size() > 0) {
                    try {
                        cn.hutool.json.JSONObject start = JSONUtil.parseObj(data.get(0));
                        cn.hutool.json.JSONObject over = JSONUtil.parseObj(data.get(data.size() - 1));
                        int startCount = start.getInt("Number");
                        int endCount = over.getInt("Number");
                        yieldCount += (endCount - startCount);
                    } catch (Exception e) {
//                        e.printStackTrace();
                    }
                }
                for (int i = data.size() - 1; i >= 0; i--) {
//                    if (("1.0").equals(JSONUtil.parseObj(data.get(i)).getStr("Power_ON"))) {
                    if (("1.0").equals(JSONUtil.parseObj(data.get(i)).getStr("Product_Status"))) {
                        onlineTime++;
                    }
                }

                String startTime = DateUtil.formatDateTime(DateUtil.offsetHour(DateUtil.beginOfDay(new Date()), 8));
                String endTime = DateUtil.formatDateTime(new Date());
                JSONArray array = iotPorductionController.getStatusTimeService(equipment.getEquipmentCode(), "Product_Status",
                        "4b880be617ba45629033ae5af382b6aa",
                        startTime, endTime, false);
                if (array != null && array.size() > 0) {
                    int freeTime = JSONUtil.parseObj(array.get(0)).getInt("0");
                    int workTime = JSONUtil.parseObj(array.get(0)).getInt("1");
                    totalTime += freeTime + workTime;
                    workingTime += workTime;
                }

            }


            // 将在线设备数量和产量数量添加到设备类型统计信息中
            one.put("onlineCount", onlineCount);
            one.put("onlineTime", onlineTime / 60);
            one.put("yieldCount", yieldCount);
            one.put("utilizationRate", workingTime * 100 / totalTime);
            one.put("errorEquipmentCount", "0");//故障设备
            // 将设备类型统计信息添加到结果列表中
            ansList.add(one);

        }

        // 返回成功的结果，携带设备数据列表
        return AjaxResult.success(ansList);
    }

    @GetMapping("/centre/data")
    public AjaxResult centreData(ScreenWorkshop screenWorkshop) {
        JSONObject ans = new JSONObject();

        String today = DateUtil.today();
        long todayYield = screenYieldService.getYieldCount(today, today);

        ans.put("todayYield", todayYield); //今日产量

        String monthFirstDay = DateUtil.format(DateUtil.beginOfMonth(new Date()), "yyyy-MM-dd");
        long monthYield = screenYieldService.getYieldCount(monthFirstDay, DateUtil.format(DateUtil.endOfMonth(new Date()), "yyyy-MM-dd"));
        ans.put("monthYield", monthYield); //当月产量

        String lastMonthFirstDay = DateUtil.format(DateUtil.offsetMonth(DateUtil.beginOfMonth(new Date()), -1), "yyyy-MM-dd");
        String lastMonthEndDay = DateUtil.format(DateUtil.offsetDay(DateUtil.beginOfMonth(new Date()), -1), "yyyy-MM-dd");
        long lastMonthYield = screenYieldService.getYieldCount(lastMonthFirstDay, lastMonthEndDay);
        ans.put("lastMonthYield", lastMonthYield); //上月产量

        Long warehousingCount=screenWarehousingService.getWarehousingCount(monthFirstDay, DateUtil.format(DateUtil.endOfMonth(new Date()), "yyyy-MM-dd"));

        ans.put("warehousing", warehousingCount); //本月入库数量

//        String lastYearFirstDay = DateUtil.format(DateUtil.offsetYear(DateUtil.beginOfYear()))
        return AjaxResult.success(ans);
    }


    private JSONArray getData(String tenant, String device, String start, String end) throws StatementExecutionException, IoTDBConnectionException {
        IotEquipment equipment = iotEquipmentService.getEquipmentByCode(device);
        if (equipment != null) {
            Date s, e;
            if (StrUtil.isEmpty(start)) {
                s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
            } else {
                s = DateUtil.parse(start);
            }
            if (StrUtil.isEmpty(end)) {
                e = new Date();
            } else {
                e = DateUtil.parse(end);
            }
            List<IotTslAttr> attrs = iotEquipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
            List<String> codes = new ArrayList<>();
            for (IotTslAttr attr : attrs) {
                codes.add(attr.getAttrCode());
            }
            JSONArray list = ioTDBUtil.listDataHistoryV2(tenant, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

            for (IotTslAttr attr : attrs) {
                if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject temp = list.getJSONObject(i);
                        for (String tag : temp.keySet()) {
                            if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                try {
                                    temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                    list.set(i, temp);
                                    break;
                                } catch (Exception e1) {

                                }

                            }
                        }
                    }
                }
            }
            return list;
        }
        return null;
    }

}
