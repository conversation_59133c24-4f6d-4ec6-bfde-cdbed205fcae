////正式环境发版用这个
// package com.boyo.web.lysso.controller;
//
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.ObjectUtil;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.boyo.common.core.controller.BaseController;
//import com.boyo.common.core.domain.AjaxResult;
//import com.boyo.common.core.domain.entity.EnterpriseUser;
//import com.boyo.common.core.domain.model.LoginBody;
//import com.boyo.framework.web.service.SysLoginService;
//import com.boyo.system.domain.TSysEnterprise;
//import com.boyo.system.service.IEnterpriseDepartmentService;
//import com.boyo.system.service.IEnterpriseUserService;
//import com.boyo.system.service.ITSysEnterpriseService;
//import com.iflytek.antelope.other.client.dto.resp.OrgMemberDTO;
//import com.iflytek.antelope.other.client.dto.resp.Result;
//import com.iflytek.antelope.other.client.dto.resp.UserDTO;
//import com.iflytek.antelope.other.client.sdk.TicketSdk;
//import com.iflytek.antelope.other.client.sdk.UserInfoSdk;
//import lombok.AllArgsConstructor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletRequest;
//import java.util.List;
//
//@RestController
//@RequestMapping("/lingyang")
//@AllArgsConstructor
//public class LYSsoController extends BaseController {
//    //private final IEnterpriseUserService enterpriseUserService;
//
//    @Autowired
//    private ITSysEnterpriseService tSysEnterpriseService;
//    @Autowired
//    private SysLoginService sysLoginService;
//    @Autowired
//    private final IEnterpriseUserService enterpriseUserService;
//    @Autowired
//    private IEnterpriseDepartmentService departmentService;
//
//    private static final String aesKey = "mxtvgakbumydqmmh";
//    private static final String appKey = "spqajzcmce";
//    private static final String appSecret = "3d3254c65ea598f8353ae677ac732ff5d0d4335e";
//
//    @PostMapping("/sso/login")
//    public AjaxResult ssoLogin(@RequestParam String token, HttpServletRequest request){
//        Result<UserDTO> result = UserInfoSdk.getUserInfo(appKey, appSecret, aesKey, token);
//        UserDTO userDTO = result.getResult();
//        String userId = userDTO.getUserId();
//        String rId = userDTO.getrId();
//        Boolean ticket = TicketSdk.checkTicket(aesKey, userId, rId);
//        //if (ticket){
//        //ticket验证成功后创建企业并登录
//        TSysEnterprise tSysEnterprise = new TSysEnterprise();
//        tSysEnterprise.setEnterpriseOpenid(super.generateOpenid());
//        tSysEnterprise.setEnterpriseName(userDTO.getOrgName());
//        tSysEnterprise.setEnterpriseAbbreviation(userDTO.getOrgName());
//        tSysEnterprise.setEnterpriseCode(userDTO.getOrgName());
//        tSysEnterprise.setEnterpriseContacts(userDTO.getName());
//        tSysEnterprise.setEnterprisePhone(userDTO.getPhone());
//        tSysEnterprise.setEnterpriseEmail("<EMAIL>");
//
//        //检查企业是否存在，存在即可直接登陆
//        if(tSysEnterpriseService.checkExist(tSysEnterprise)){
//            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("enterprise_name", userDTO.getOrgName());
//            TSysEnterprise one = tSysEnterpriseService.getOne(queryWrapper);
//            List<OrgMemberDTO> orgMembers = userDTO.getOrgMembers();
//            QueryWrapper<EnterpriseUser> query = new QueryWrapper<>();
//            query.eq("enterprise_openid",one.getEnterpriseOpenid());
//            query.eq("user_name",userDTO.getName());
//            EnterpriseUser employee = enterpriseUserService.getOne(query);
//            if (ObjectUtil.isNull(employee)){
//                //创建员工
//                EnterpriseUser enterpriseUser = new EnterpriseUser();
//                enterpriseUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
//                enterpriseUser.setUserPhone(userDTO.getPhone());
//                enterpriseUser.setUserStatus("1");
//                enterpriseUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
//                enterpriseUser.setUserFullName(userDTO.getName());
//                enterpriseUser.setUserOpenid(userDTO.getrId());
//                enterpriseUser.setCreateTime(DateUtil.date());
//                enterpriseUser.setUpdateTime(DateUtil.date());
//                enterpriseUser.setUserName(userDTO.getName());
//                enterpriseUserService.saveEnterpriseUser(enterpriseUser);
//            }
////                //没有员工直接用主体信息登录
////                if (orgMembers.isEmpty()){
////                    LoginBody loginBody = new LoginBody();
////                    loginBody.setCode("");
////                    loginBody.setPassword("123456");
////                    loginBody.setEnterpriseCode(userDTO.getOrgName());
////                    loginBody.setUuid(userDTO.getrId());
////                    loginBody.setUsername(userDTO.getOrgName());
////                    String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
////                    LoginInfo loginInfo = new LoginInfo();
////                    loginInfo.setToken(iotToken);
////                    loginInfo.setEnterpriseCode(userDTO.getOrgName());
////                    loginInfo.setUsername(userDTO.getPhone());
////                    return AjaxResult.success("验证成功，已经存在企业，正在登录...",loginInfo);
////                }
//
//            LoginBody loginBody = new LoginBody();
//            loginBody.setCode("");
//            loginBody.setPassword("123456");
//            loginBody.setEnterpriseCode(one.getEnterpriseCode());
//            loginBody.setUuid(userDTO.getrId());
//            loginBody.setUsername(userDTO.getName());
//            String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
//            LoginInfo loginInfo = new LoginInfo();
//            loginInfo.setToken(iotToken);
//            loginInfo.setEnterpriseCode(userDTO.getOrgName());
//            loginInfo.setUsername(userDTO.getName());
//            return AjaxResult.success("验证成功，已经存在企业，正在登录...",loginInfo);
//        }
//        //创建企业后进行登录
//        if (tSysEnterpriseService.save(tSysEnterprise)){
//            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("enterprise_name", userDTO.getOrgName());
//            TSysEnterprise one = tSysEnterpriseService.getOne(queryWrapper);
//
//            QueryWrapper<EnterpriseUser> query = new QueryWrapper<>();
//            query.eq("enterprise_openid",one.getEnterpriseOpenid());
//            query.eq("user_name",userDTO.getName());
//            EnterpriseUser employee = enterpriseUserService.getOne(query);
//            if (ObjectUtil.isNull(employee)){
//                //创建员工
//                EnterpriseUser enterpriseUser = new EnterpriseUser();
//                enterpriseUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
//                enterpriseUser.setUserPhone(userDTO.getPhone());
//                enterpriseUser.setUserStatus("1");
//                enterpriseUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
//                enterpriseUser.setUserFullName(userDTO.getName());
//                enterpriseUser.setUserOpenid(userDTO.getrId());
//                enterpriseUser.setCreateTime(DateUtil.date());
//                enterpriseUser.setUpdateTime(DateUtil.date());
//                enterpriseUser.setUserName(userDTO.getName());
//                enterpriseUserService.saveEnterpriseUser(enterpriseUser);
//            }
//
//
//            LoginBody loginBody = new LoginBody();
//            loginBody.setCode("");
//            loginBody.setPassword("123456");
//            loginBody.setEnterpriseCode(one.getEnterpriseCode());
//            loginBody.setUuid(userDTO.getrId());
//            loginBody.setUsername(userDTO.getOrgName());
//            String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
//            LoginInfo loginInfo = new LoginInfo();
//            loginInfo.setToken(iotToken);
//            loginInfo.setEnterpriseCode(userDTO.getOrgName());
//            loginInfo.setUsername(userDTO.getPhone());
//            return AjaxResult.success("验证成功，成功创建企业，正在登录...",loginInfo);
//        }
//        //}
//        return AjaxResult.error("验证失败",ticket);
//
//    }
//
//}
//class LoginInfo{
//    private String token;
//    private String enterpriseCode;
//    private String username;
//
//    public String getToken() {
//        return token;
//    }
//
//    public void setToken(String token) {
//        this.token = token;
//    }
//
//    public String getEnterpriseCode() {
//        return enterpriseCode;
//    }
//
//    public void setEnterpriseCode(String enterpriseCode) {
//        this.enterpriseCode = enterpriseCode;
//    }
//
//    public String getUsername() {
//        return username;
//    }
//
//    public void setUsername(String username) {
//        this.username = username;
//    }
//}
//
//
//
//
////测试环境发版用这个
////package com.boyo.web.lysso.controller;
////
////import cn.hutool.core.collection.ListUtil;
////import cn.hutool.core.date.DateUtil;
////import cn.hutool.core.lang.UUID;
////import cn.hutool.core.util.IdUtil;
////import cn.hutool.core.util.ObjectUtil;
////import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
////import com.boyo.common.core.controller.BaseController;
////import com.boyo.common.core.domain.AjaxResult;
////import com.boyo.common.core.domain.entity.EnterpriseUser;
////import com.boyo.common.core.domain.model.LoginBody;
////import com.boyo.common.utils.StringUtils;
////import com.boyo.framework.web.service.SysLoginService;
////import com.boyo.system.domain.*;
////import com.boyo.system.service.*;
////import com.iflytek.antelope.other.client.dto.resp.OrgMemberDTO;
////import com.iflytek.antelope.other.client.dto.resp.Result;
////import com.iflytek.antelope.other.client.dto.resp.UserDTO;
////import com.iflytek.antelope.other.client.sdk.TicketSdk;
////import com.iflytek.antelope.other.client.sdk.UserInfoSdk;
////import lombok.AllArgsConstructor;
////import org.springframework.beans.factory.annotation.Autowired;
////import org.springframework.web.bind.annotation.*;
////
////import javax.servlet.http.HttpServletRequest;
////import java.util.*;
////
////@RestController
////@RequestMapping("/lingyang")
////@AllArgsConstructor
////public class LYSsoController extends BaseController {
////    //private final IEnterpriseUserService enterpriseUserService;
////
////    @Autowired
////    private ITSysEnterpriseService tSysEnterpriseService;
////    @Autowired
////    private SysLoginService sysLoginService;
////    @Autowired
////    private final IEnterpriseUserService enterpriseUserService;
////    @Autowired
////    private IEnterpriseDepartmentService departmentService;
////    @Autowired
////    private ISysEnterpriseAuthorityService sysEnterpriseAuthorityService;
////    @Autowired
////    private IEnterpriseRoleService enterpriseRoleService;
////    @Autowired
////    private IEnterpriseRoleFunctionService roleFunctionService;
////
////    private static final String aesKey = "mxtvgakbumydqmmh";
////    private static final String appKey = "spqajzcmce";
////    private static final String appSecret = "3d3254c65ea598f8353ae677ac732ff5d0d4335e";
////
////    private static final String[] function = {"2168","2252","2258","2188","2189","2190","2191","1","100","101",
////            "102","103","104","105","106","2364","2365","2337","2338","2352",
////            "2339","2340","2354","2355","2356","2341","2342","2343","2344","2345",
////            "2346","2347","2357","2348","2349","2350","2351","2361","2362","2363",
////            "2383","2384","2385","2386","2387","2388","2391","2392","2394"};
////
////    @PostMapping("/sso/login")
////    public AjaxResult ssoLogin(@RequestParam String token, HttpServletRequest request){
////        Result<UserDTO> result = UserInfoSdk.getUserInfo(appKey, appSecret, aesKey, token);
////        UserDTO userDTO = result.getResult();
////        String userId = userDTO.getUserId();
////        String rId = userDTO.getrId();
////        Boolean ticket = TicketSdk.checkTicket(aesKey, userId, rId);
////
////        TSysEnterprise tSysEnterprise = new TSysEnterprise();
////        tSysEnterprise.setEnterpriseOpenid(super.generateOpenid());
////        tSysEnterprise.setEnterpriseName(userDTO.getOrgName());
////        tSysEnterprise.setEnterpriseAbbreviation(userDTO.getOrgName());
////        tSysEnterprise.setEnterpriseCode(userDTO.getOrgName());
////        tSysEnterprise.setEnterpriseContacts(userDTO.getName());
////        tSysEnterprise.setEnterprisePhone(userDTO.getPhone());
////        tSysEnterprise.setEnterpriseEmail("<EMAIL>");
////
////        //检查企业是否存在，存在即可直接登陆
////        if(tSysEnterpriseService.checkExist(tSysEnterprise)){
////            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
////            queryWrapper.eq("enterprise_name", userDTO.getOrgName());
////            TSysEnterprise one = tSysEnterpriseService.getOne(queryWrapper);
////            List<OrgMemberDTO> orgMembers = userDTO.getOrgMembers();
////            QueryWrapper<EnterpriseUser> query = new QueryWrapper<>();
////            query.eq("enterprise_openid",one.getEnterpriseOpenid());
////            query.eq("user_name",userDTO.getName());
////            EnterpriseUser employee = enterpriseUserService.getOne(query);
////            if (ObjectUtil.isNull(employee)){
////                //创建员工
////                EnterpriseUser enterpriseUser = new EnterpriseUser();
////                enterpriseUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                enterpriseUser.setUserPhone(userDTO.getPhone());
////                enterpriseUser.setUserStatus("1");
////                enterpriseUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
////                enterpriseUser.setUserFullName(userDTO.getName());
////                enterpriseUser.setUserOpenid(userDTO.getrId());
////                enterpriseUser.setCreateTime(DateUtil.date());
////                enterpriseUser.setUpdateTime(DateUtil.date());
////                enterpriseUser.setUserName(userDTO.getName());
////                enterpriseUserService.saveEnterpriseUser(enterpriseUser);
////            }
////            //初始化数据库
////            if (StringUtils.isEmpty(one.getEnterpriseDatabaseUrl())){
////                tSysEnterpriseService.initDatabase(one.getEnterpriseOpenid());
////            }
////            //分配权限
////            SysEnterpriseAuthority authority = new SysEnterpriseAuthority();
////            authority.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            List<SysEnterpriseAuthority> sysEnterpriseAuthorities = sysEnterpriseAuthorityService.selectSysEnterpriseAuthorityList(authority);
////            if (sysEnterpriseAuthorities == null || sysEnterpriseAuthorities.size() <= 0){
////                Calendar calendar = Calendar.getInstance();
////                // 将当前时间向后偏移15天
////                calendar.add(Calendar.DAY_OF_MONTH, 15);
////                // 将 Calendar 类型转换为 Date 类型
////                Date date = calendar.getTime();
////
////                //权限为空，进行授权
////                List<SysEnterpriseAuthority> sysEnterpriseAuthority = new ArrayList<>();
////                SysEnterpriseAuthority authority1 = new SysEnterpriseAuthority();
////                authority1.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                authority1.setSystemOpenid("DCBB04D3554B42F0A376978D725F50B6"); //企业管理
////                authority1.setSystemValidity(date);
////                SysEnterpriseAuthority authority2 = new SysEnterpriseAuthority();
////                authority2.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                authority2.setSystemOpenid("05149ef65a344abd93a35fdb41d6a63c"); //生产管理
////                authority2.setSystemValidity(date);
////                sysEnterpriseAuthority.add(authority1);
////                sysEnterpriseAuthority.add(authority2);
////                sysEnterpriseAuthorityService.saveBatch(sysEnterpriseAuthority);
////
////            }
////            EnterpriseRole role = new EnterpriseRole();
////            role.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            List<EnterpriseRole> enterpriseRoles = enterpriseRoleService.selectEnterpriseRoleList(role);
////            if (enterpriseRoles == null || enterpriseRoles.size() == 0){
////                //创建管理员权限
////                EnterpriseRole enterpriseRole = new EnterpriseRole();
////                enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                enterpriseRole.setDataScope("1");
////                enterpriseRole.setRoleName("管理员");
////                enterpriseRole.setRoleDesc("总管理员，最高权限");
////                enterpriseRole.setRoleOpenid(IdUtil.simpleUUID());
////                enterpriseRoleService.saveRoleAdmin(enterpriseRole);
////                String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
////                List<EnterpriseRoleFunction> enterpriseRoleFunctionList = new ArrayList<>();
////                for (String f : function) {
////                    EnterpriseRoleFunction roleFunction = new EnterpriseRoleFunction();
////                    roleFunction.setRoleOpenid(roleOpenid);
////                    roleFunction.setFunctionOpenid(f);
////                    roleFunction.setCreateTime(new Date());
////                    enterpriseRoleFunctionList.add(roleFunction);
////                }
////                roleFunctionService.saveBatch(enterpriseRoleFunctionList);
////            }
////
////            //创建管理员权限
////            EnterpriseRole enterpriseRole = new EnterpriseRole();
////            enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            enterpriseRole.setDataScope("1");
////            enterpriseRole.setRoleName("管理员");
////            enterpriseRole.setRoleDesc("总管理员，最高权限");
////            String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
////            //给这个员工赋予管理员权限
////            QueryWrapper<EnterpriseUser> queryUser = new QueryWrapper<>();
////            queryUser.eq("enterprise_openid",one.getEnterpriseOpenid());
////            queryUser.eq("user_name",userDTO.getName());
////            queryUser.eq("user_phone",userDTO.getPhone());
////            EnterpriseUser enterpriseUser = enterpriseUserService.getOne(queryUser);
////            EnterpriseDepartment department = new EnterpriseDepartment();
////            department.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            enterpriseUser.setDepartmentOpenid(departmentService.selectEnterpriseDepartmentList(department).get(0).getDepartmentOpenid());
////            List<String> roleList = new ArrayList<>();
////            roleList.add(roleOpenid);
////            enterpriseUser.setRoleList(roleList);
////            enterpriseUserService.updateEnterpriseUserById(enterpriseUser);
////
////
////
////            LoginBody loginBody = new LoginBody();
////            loginBody.setCode("");
////            loginBody.setPassword("123456");
////            loginBody.setEnterpriseCode(one.getEnterpriseCode());
////            loginBody.setUuid(userDTO.getrId());
////            loginBody.setUsername(userDTO.getName());
////            String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
////            LoginInfo loginInfo = new LoginInfo();
////            loginInfo.setToken(iotToken);
////            loginInfo.setEnterpriseCode(userDTO.getOrgName());
////            loginInfo.setUsername(userDTO.getName());
////            return AjaxResult.success("验证成功，已经存在企业，正在登录...",loginInfo);
////        }
////        //创建企业后进行登录
////        if (tSysEnterpriseService.save(tSysEnterprise)){
////            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
////            queryWrapper.eq("enterprise_name", userDTO.getOrgName());
////            TSysEnterprise one = tSysEnterpriseService.getOne(queryWrapper);
////
////            QueryWrapper<EnterpriseUser> query = new QueryWrapper<>();
////            query.eq("enterprise_openid",one.getEnterpriseOpenid());
////            query.eq("user_name",userDTO.getName());
////            EnterpriseUser employee = enterpriseUserService.getOne(query);
////            if (ObjectUtil.isNull(employee)){
////                //创建员工
////                EnterpriseUser enterpriseUser = new EnterpriseUser();
////                enterpriseUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                enterpriseUser.setUserPhone(userDTO.getPhone());
////                enterpriseUser.setUserStatus("1");
////                enterpriseUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
////                enterpriseUser.setUserFullName(userDTO.getName());
////                enterpriseUser.setUserOpenid(userDTO.getrId());
////                enterpriseUser.setCreateTime(DateUtil.date());
////                enterpriseUser.setUpdateTime(DateUtil.date());
////                enterpriseUser.setUserName(userDTO.getName());
////                enterpriseUserService.saveEnterpriseUser(enterpriseUser);
////            }
////
////            //初始化数据库
////            if (StringUtils.isEmpty(one.getEnterpriseDatabaseUrl())){
////                tSysEnterpriseService.initDatabase(one.getEnterpriseOpenid());
////            }
////            //分配权限
////            SysEnterpriseAuthority authority = new SysEnterpriseAuthority();
////            authority.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            List<SysEnterpriseAuthority> sysEnterpriseAuthorities = sysEnterpriseAuthorityService.selectSysEnterpriseAuthorityList(authority);
////            if (sysEnterpriseAuthorities == null || sysEnterpriseAuthorities.size() <= 0){
////                Calendar calendar = Calendar.getInstance();
////                // 将当前时间向后偏移15天
////                calendar.add(Calendar.DAY_OF_MONTH, 15);
////                // 将 Calendar 类型转换为 Date 类型
////                Date date = calendar.getTime();
////
////                //权限为空，进行授权
////                List<SysEnterpriseAuthority> sysEnterpriseAuthority = new ArrayList<>();
////                SysEnterpriseAuthority authority1 = new SysEnterpriseAuthority();
////                authority1.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                authority1.setSystemOpenid("DCBB04D3554B42F0A376978D725F50B6"); //企业管理
////                authority1.setSystemValidity(date);
////                SysEnterpriseAuthority authority2 = new SysEnterpriseAuthority();
////                authority2.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                authority2.setSystemOpenid("05149ef65a344abd93a35fdb41d6a63c"); //生产管理
////                authority2.setSystemValidity(date);
////                sysEnterpriseAuthority.add(authority1);
////                sysEnterpriseAuthority.add(authority2);
////                sysEnterpriseAuthorityService.saveBatch(sysEnterpriseAuthority);
////
////            }
////            EnterpriseRole role = new EnterpriseRole();
////            role.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            List<EnterpriseRole> enterpriseRoles = enterpriseRoleService.selectEnterpriseRoleList(role);
////            if (enterpriseRoles == null || enterpriseRoles.size() == 0){
////                //创建管理员权限
////                EnterpriseRole enterpriseRole = new EnterpriseRole();
////                enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
////                enterpriseRole.setDataScope("1");
////                enterpriseRole.setRoleName("管理员");
////                enterpriseRole.setRoleDesc("总管理员，最高权限");
////                enterpriseRole.setRoleOpenid(IdUtil.simpleUUID());
////                enterpriseRoleService.saveRoleAdmin(enterpriseRole);
////                String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
////                List<EnterpriseRoleFunction> enterpriseRoleFunctionList = new ArrayList<>();
////                for (String f : function) {
////                    EnterpriseRoleFunction roleFunction = new EnterpriseRoleFunction();
////                    roleFunction.setRoleOpenid(roleOpenid);
////                    roleFunction.setFunctionOpenid(f);
////                    roleFunction.setCreateTime(new Date());
////                    roleFunction.setUpdateTime(new Date());
////                    enterpriseRoleFunctionList.add(roleFunction);
////                }
////                roleFunctionService.saveBatch(enterpriseRoleFunctionList);
////            }
////
////            EnterpriseRole enterpriseRole = new EnterpriseRole();
////            enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            enterpriseRole.setDataScope("1");
////            enterpriseRole.setRoleName("管理员");
////            enterpriseRole.setRoleDesc("总管理员，最高权限");
////            String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
////
////            //给这个员工赋予管理员权限
////            QueryWrapper<EnterpriseUser> queryUser = new QueryWrapper<>();
////            queryUser.eq("enterprise_openid",one.getEnterpriseOpenid());
////            queryUser.eq("user_name",userDTO.getName());
////            queryUser.eq("user_phone",userDTO.getPhone());
////            EnterpriseUser enterpriseUser = enterpriseUserService.getOne(queryUser);
////            EnterpriseDepartment department = new EnterpriseDepartment();
////            department.setEnterpriseOpenid(one.getEnterpriseOpenid());
////            enterpriseUser.setDepartmentOpenid(departmentService.selectEnterpriseDepartmentList(department).get(0).getDepartmentOpenid());
////            List<String> roleList = new ArrayList<>();
////            roleList.add(roleOpenid);
////            enterpriseUser.setRoleList(roleList);
////            enterpriseUserService.updateEnterpriseUserById(enterpriseUser);
////
////            LoginBody loginBody = new LoginBody();
////            loginBody.setCode("");
////            loginBody.setPassword("123456");
////            loginBody.setEnterpriseCode(one.getEnterpriseCode());
////            loginBody.setUuid(userDTO.getrId());
////            loginBody.setUsername(userDTO.getOrgName());
////            String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
////            LoginInfo loginInfo = new LoginInfo();
////            loginInfo.setToken(iotToken);
////            loginInfo.setEnterpriseCode(userDTO.getOrgName());
////            loginInfo.setUsername(userDTO.getPhone());
////            return AjaxResult.success("验证成功，成功创建企业，正在登录...",loginInfo);
////        }
////        return AjaxResult.error("验证失败",ticket);
////
////    }
////
////}
////class LoginInfo{
////    private String token;
////    private String enterpriseCode;
////    private String username;
////
////    public String getToken() {
////        return token;
////    }
////
////    public void setToken(String token) {
////        this.token = token;
////    }
////
////    public String getEnterpriseCode() {
////        return enterpriseCode;
////    }
////
////    public void setEnterpriseCode(String enterpriseCode) {
////        this.enterpriseCode = enterpriseCode;
////    }
////
////    public String getUsername() {
////        return username;
////    }
////
////    public void setUsername(String username) {
////        this.username = username;
////    }
////}