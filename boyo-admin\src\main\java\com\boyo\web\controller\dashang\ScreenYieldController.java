package com.boyo.web.controller.dashang;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.annotation.Log;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.enums.BusinessType;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.master.domain.ScreenWorkshop;
import com.boyo.master.domain.ScreenYield;
import com.boyo.master.service.IScreenWorkshopService;
import com.boyo.master.service.IScreenYieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 大屏产量Controller
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@RestController
@RequestMapping("/dashang/yield")
public class ScreenYieldController extends BaseController {
    @Autowired
    private IScreenYieldService screenYieldService;
    @Autowired
    IScreenWorkshopService screenWorkshopService;

    /**
     * 查询大屏产量列表
     */
//    @PreAuthorize("@ss.hasPermi('system:yield:list')")
    @GetMapping("/list")
    public AjaxResult list(ScreenYield screenYield) {
        boolean last7Days = false;
        if (screenYield.getRemark() != null) {
            last7Days = true;
        }
        if (screenYield.getYieldTime() == null) {
            screenYield.setYieldTime(new Date());
        }
        JSONObject jsonObject = new JSONObject();
        List<JSONObject> yieldList = new ArrayList<>();
        List<JSONObject> workshopList = new ArrayList<>();

        List<ScreenWorkshop> workshopsList = screenWorkshopService.selectScreenWorkshopList(null);
        workshopsList.forEach(item -> {
            JSONObject temp = new JSONObject();
            temp.put("prop", item.getWorkshopId());
            temp.put("label", item.getWorkshopName());
            workshopList.add(temp);
        });
        jsonObject.put("columns", workshopList);

        DateTime dateTime = DateUtil.beginOfMonth(screenYield.getYieldTime());
        screenYield.setYieldTime(dateTime);
        if (!last7Days) {
            for (int i = 0; i < 31; i++) {
                if (screenYield.getYieldTime().after(new Date()) ||
                        screenYield.getYieldTime().after(DateUtil.endOfMonth((screenYield.getYieldTime())))) {
                    break;
                }
                JSONObject temp = new JSONObject();
                // 初始化数据       默认为0
                workshopList.forEach(item1 -> {
                    temp.put(item1.get("prop") + "", 0);
                });
                List<ScreenYield> list1 = screenYieldService.selectScreenYieldList(screenYield);
                temp.put("date", screenYield.getYieldTime().toString().substring(0, 10));
                list1.forEach(item -> {
                    temp.put(item.getWorkshopId() + "", item.getYield());
                });
                yieldList.add(temp);
                screenYield.setYieldTime(DateUtil.offsetDay(screenYield.getYieldTime(), 1));
            }
        } else {
            for (int i = 6; i >= 0; i--) {
                screenYield.setYieldTime(DateUtil.offsetDay(new Date(), -i));
                List<ScreenYield> list7Days = screenYieldService.selectScreenYieldList(screenYield);
                JSONObject temp = new JSONObject();
                // 初始化数据       默认为0
                workshopList.forEach(item1 -> {
                    temp.put(item1.get("prop") + "", 0);
                });
                temp.put("date", screenYield.getYieldTime().toString().substring(0, 10));
                list7Days.forEach(item -> {
                    temp.put(item.getWorkshopId() + "", item.getYield());
                });
                yieldList.add(temp);
            }
        }
        jsonObject.put("tableData", yieldList);

        return AjaxResult.success(jsonObject);
    }

    /**
     * 导出大屏产量列表
     */
//    @PreAuthorize("@ss.hasPermi('system:yield:export')")
//    @Log(title = "大屏产量", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ScreenYield screenYield)
//    {
//        List<ScreenYield> list = screenYieldService.selectScreenYieldList(screenYield);
//        ExcelUtil<ScreenYield> util = new ExcelUtil<ScreenYield>(ScreenYield.class);
//        util.exportExcel(response, list, "大屏产量数据");
//    }

    /**
     * 获取大屏产量详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:yield:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(screenYieldService.selectScreenYieldById(id));
    }

    /**
     * 新增大屏产量
     */
    @PostMapping
    public AjaxResult add(@RequestBody List<ScreenYield> screenYieldList) {
        if (screenYieldList == null) {
            return AjaxResult.error("数据为空");
        }
        int success = 0;
        int error = 0;
        for (ScreenYield item : screenYieldList) {
//            ScreenWorkshop screenWorkshop = new ScreenWorkshop();
//            screenWorkshop.setWorkshopId(item.getId());
            final ScreenWorkshop exeitscreenWorkshop = screenWorkshopService.selectScreenWorkshopByWorkshopId(item.getWorkshopId());
//            List<ScreenWorkshop> list = screenWorkshopService.selectScreenWorkshopList(screenWorkshop);
            ScreenYield screenYield = new ScreenYield();
            screenYield.setYieldTime(item.getYieldTime());
            screenYield.setWorkshopId(item.getWorkshopId());
            List<ScreenYield> screenYields = screenYieldService.selectScreenYieldList(screenYield);
            if (exeitscreenWorkshop != null && screenYields.size() == 0) {
                int i = screenYieldService.insertScreenYield(item);
                success += i;
            } else {
                error++;
            }

        }
        return AjaxResult.success("成功" + success + "条，失败" + error + "条");
    }

    /**
     * 修改大屏产量
     */
    @Log(title = "大屏产量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JSONObject editScreenYield) {
        Map<String, String> map = new HashMap<>();
        map=(Map)JSONObject.parse(String.valueOf(editScreenYield));
        int success = 0;

        ScreenYield screenYield = new ScreenYield();
        screenYield.setYieldTime(DateUtil.parse(map.get("date") ,"yyyy-MM-dd"));
        //遍历map
        for(Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key.equals("date")) {
                continue;
            } else {
                screenYield.setWorkshopId(Long.parseLong(key));
                final List<ScreenYield> screenYields = screenYieldService.selectScreenYieldList(screenYield);
                if(screenYields.size()==0){
                    screenYield.setYield(Long.parseLong(value));
                    success+=screenYieldService.insertScreenYield(screenYield);
                }else {
                    screenYield.setYield(Long.parseLong(value));
                    success+=screenYieldService.updateScreenYield(screenYield);
                }
            }
        }
        return AjaxResult.success(success);
    }

    /**
     * 删除大屏产量
     */
    @Log(title = "大屏产量", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(screenYieldService.deleteScreenYieldByIds(ids));
    }

    /**
     * 删除大屏产量
     */
    @Log(title = "大屏产量", businessType = BusinessType.DELETE)
    @DeleteMapping("/del/{yieldTime}")
    public AjaxResult remove(@PathVariable String yieldTime) {
        return toAjax(screenYieldService.deleteScreenYieldByyieldTime(yieldTime));
    }


}
