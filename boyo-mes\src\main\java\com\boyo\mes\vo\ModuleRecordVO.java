package com.boyo.mes.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class ModuleRecordVO {
    private Integer id;
    private String equipmentName;
    private String equipmentCode;
    private Integer modulId;
    private Integer productionId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private String mouldName;
    private String mouldcCode;
    private String productionName;
    private String productionCode;
    private Integer recordId;
    private Integer productionMultiple;

    private List<Product> productionIds;

    public static class Product {
        private Integer productionId;
        private Integer productionMultiple;

        public Integer getProductionId() {
            return productionId;
        }

        public void setProductionId(Integer productionId) {
            this.productionId = productionId;
        }

        public Integer getProductionMultiple() {
            return productionMultiple;
        }

        public void setProductionMultiple(Integer productionMultiple) {
            this.productionMultiple = productionMultiple;
        }
    }
}

