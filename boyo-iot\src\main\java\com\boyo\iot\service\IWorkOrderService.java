package com.boyo.iot.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.iot.entity.WorkOrder;
import java.util.List;

/**
 * 系统工单(WorkOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-08 15:10:05
 */
public interface IWorkOrderService extends IService<WorkOrder> {

    /**
     * 查询多条数据
     *
     * @param workOrder 对象信息
     * @return 对象列表
     */
    List<WorkOrder> selectWorkOrderList(WorkOrder workOrder);

    List<WorkOrder> listWaitReceive(WorkOrder workOrder);

    void receiveOrder(Integer id);
    void cancelOrder(Integer id);


}
