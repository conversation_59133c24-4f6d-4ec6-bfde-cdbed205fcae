# 项目相关配置
boyo:
  # 名称
  name: boyo
  # 版本
  version: 3.3.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/boyo/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/boyo/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

  tenant:
    database:
      init:
        schema:
          url: https://jms.obs.cn-north-4.myhuaweicloud.com/database/schema.sql
        driver-class: com.mysql.cj.jdbc.Driver
        url: ***************************/{{database}}?useUnicode=true&useSSL=false&characterEncoding=utf8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    root: warn
    com.boyo: info
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: jiangyou
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    port: 6379
    database: 1
    # 密码
    #    password: 123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  iotdb:
    ip: ${SPRING_IOTDB_IP:***********}
    port: ${SPRING_IOTDB_PORT:6667}
    user: ${SPRING_IOTDB_USER:root}
    password: ${SPRING_IOTDB_PASSWORD:root}
    fetchSize: 10000

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
#mybatis:
#    # 搜索指定包别名
#    typeAliasesPackage: com.boyo.**.domain
#    # 配置mapper的扫描，找到所有的mapper.xml映射文件
#    mapperLocations: classpath*:mapper/**/*Mapper.xml
#    # 加载全局的配置文件
#    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger/usr/project/api
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

mybatis-plus:
  #  config-location: classpath:mapper/mybatis-config.xml
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.boyo
  global-config:
    db-config:
      id-type: auto

wx:
  mp:
    useRedis: false
    redisConfig:
      host: ${REDIS_SERVICE_HOST:127.0.0.1}
      port: 6379
      password: ${REDIS_SERVICE_PASSWORD:123456}
    #      password: ${REDIS_SERVICE_PASSWORD:jms2020IOT}
    configs:
      - appId: wxb965ba4b2b8a7c3a # 第一个公众号的appid
        secret: d54ea7671a27d07da7f45e1006639f43 # 公众号的appsecret
        token: nmd_xipin # 接口配置里的Token值
        aesKey: syd1X99Sf7EojbEXSgpfkILDqBSZgeyiNOg9xoWen6W # 接口配置里的EncodingAESKey值

mqtt:
  url: ${MQTT_URL:tcp://127.0.0.1:1883}
  topic: ${MQTT_TOPIC:ningmeng/#,hsl/#,cnc/#,room/#}
  #  topic: ${MQTT_TOPIC:room/#}
  username: zhenghe
  password: ZHENGheyuh@76basd

aliyun:
  oss:
    bucketName: boyo
    endPoint: oss-cn-beijing.aliyuncs.com
    accessKeyId: LTAI5t9W1WwnQFAyhPM1BkDJ
    accessKeySecret: ******************************
