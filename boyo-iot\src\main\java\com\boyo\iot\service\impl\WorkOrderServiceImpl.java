package com.boyo.iot.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.entity.OrderMaterial;
import com.boyo.iot.mapper.OrderMaterialMapper;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.mapper.BaseDictMapper;
import com.boyo.system.service.IEnterpriseUserService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.iot.entity.WorkOrder;
import com.boyo.iot.mapper.WorkOrderMapper;
import com.boyo.iot.service.IWorkOrderService;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 系统工单(WorkOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-08 15:10:06
 */
@Service("workOrderService")
@AllArgsConstructor
public class WorkOrderServiceImpl extends ServiceImpl<WorkOrderMapper, WorkOrder> implements IWorkOrderService {
    private final WorkOrderMapper workOrderMapper;
    private final IEnterpriseUserService enterpriseUserService;
    private final OrderMaterialMapper orderMaterialMapper;
    private final BaseDictMapper baseDictMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope
    public List<WorkOrder> selectWorkOrderList(WorkOrder workOrder) {
        List<WorkOrder> list = workOrderMapper.selectWorkOrderList(workOrder);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
                ids.add(list.get(i).getCreateUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(ObjectUtil.isNotNull(list.get(i).getOwnerUserId()) && list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                        }
                        if(ObjectUtil.isNotNull(list.get(i).getCreateUserId()) && list.get(i).getCreateUserId().equals(userList.get(j).getId())){
                            list.get(i).setCreateUserName(userList.get(j).getUserFullName());
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<WorkOrder> listWaitReceive(WorkOrder workOrder) {
        List<WorkOrder> list = workOrderMapper.listWaitReceive(workOrder);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
                ids.add(list.get(i).getCreateUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(ObjectUtil.isNotNull(list.get(i).getOwnerUserId()) && list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                        }
                        if(ObjectUtil.isNotNull(list.get(i).getCreateUserId()) && list.get(i).getCreateUserId().equals(userList.get(j).getId())){
                            list.get(i).setCreateUserName(userList.get(j).getUserFullName());
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public void receiveOrder(Integer id) {
        WorkOrder workOrder = workOrderMapper.selectById(id);
        if(ObjectUtil.isNotNull(workOrder)){
            workOrder.setOwnerUserId(SecurityUtils.getUserId());
            workOrder.setReceiveTime(new Date());
            workOrderMapper.updateById(workOrder);
        }
    }

    @Override
    public void cancelOrder(Integer id) {
        WorkOrder workOrder = workOrderMapper.selectById(id);
        if(ObjectUtil.isNotNull(workOrder)){
            workOrder.setOwnerUserId(null);
            workOrder.setReceiveTime(null);
            workOrderMapper.updateById(workOrder);
        }
    }

    @Override
    public WorkOrder getById(Serializable id) {
        WorkOrder workOrder = super.getById(id);
        if (ObjectUtil.isNotNull(workOrder.getReasonId())){
            BaseDict baseDict = baseDictMapper.selectById(workOrder.getReasonId());
            if(ObjectUtil.isNotNull(baseDict)){
                workOrder.setReasonStr(baseDict.getBaseDesc());
            }
        }
        if (ObjectUtil.isNotNull(workOrder.getOwnerUserId())){
            EnterpriseUser user = enterpriseUserService.getById(workOrder.getOwnerUserId());
            if(ObjectUtil.isNotNull(user)){
                workOrder.setOwnerUserName(user.getUserFullName());
            }
        }
        if(ObjectUtil.isNotNull(workOrder.getCreateUserId())){
            EnterpriseUser user = enterpriseUserService.getById(workOrder.getCreateUserId());
            if(ObjectUtil.isNotNull(user)){
                workOrder.setCreateUserName(user.getUserFullName());
            }
        }
        return workOrder;
    }

    @Override
    public boolean updateById(WorkOrder entity) {
        QueryWrapper<OrderMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id",entity.getId());
        orderMaterialMapper.delete(queryWrapper);
        List<OrderMaterial> materialList = entity.getMaterialList();
        if(materialList != null && materialList.size() > 0){
            for (OrderMaterial material:materialList) {
                material.setOrderId(entity.getId());
                orderMaterialMapper.insert(material);
            }
        }
        return super.updateById(entity);
    }
}


