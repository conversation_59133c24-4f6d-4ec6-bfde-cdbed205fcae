package com.boyo.iot.service;

import com.boyo.iot.entity.IorealData;
import com.boyo.iot.mapper.IorealDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * IoT实时数据批量处理服务
 * 优化频繁的数据库更新操作
 */
@Service
@Slf4j
public class IotRealDataBatchService {

    @Autowired
    private IorealDataMapper iorealDataMapper;

    // 批量处理队列
    private final BlockingQueue<IorealData> dataQueue = new LinkedBlockingQueue<>(10000);
    
    // 批量处理线程池
    private ScheduledExecutorService batchProcessor;
    
    // 配置参数
    private static final int BATCH_SIZE = 500; // 批量大小
    private static final int BATCH_TIMEOUT_MS = 1000; // 批量超时时间(毫秒)
    private static final int MAX_RETRY_COUNT = 3; // 最大重试次数
    
    // 统计信息
    private final AtomicInteger totalProcessed = new AtomicInteger(0);
    private final AtomicInteger totalFailed = new AtomicInteger(0);

    @PostConstruct
    public void init() {
        // 启动批量处理线程
        batchProcessor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "iot-batch-processor");
            t.setDaemon(true);
            return t;
        });
        
        // 定时批量处理
        batchProcessor.scheduleWithFixedDelay(this::processBatch, 
            BATCH_TIMEOUT_MS, BATCH_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        
        log.info("IoT实时数据批量处理服务已启动，批量大小: {}, 超时时间: {}ms", BATCH_SIZE, BATCH_TIMEOUT_MS);
    }

    @PreDestroy
    public void destroy() {
        if (batchProcessor != null) {
            batchProcessor.shutdown();
            try {
                if (!batchProcessor.awaitTermination(5, TimeUnit.SECONDS)) {
                    batchProcessor.shutdownNow();
                }
            } catch (InterruptedException e) {
                batchProcessor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 处理剩余数据
        if (!dataQueue.isEmpty()) {
            processBatch();
        }
        
        log.info("IoT实时数据批量处理服务已关闭，总处理: {}, 总失败: {}", 
            totalProcessed.get(), totalFailed.get());
    }

    /**
     * 添加数据到批量处理队列
     */
    public boolean addData(IorealData data) {
        if (data == null || data.getKey() == null) {
            return false;
        }
        
        boolean added = dataQueue.offer(data);
        if (!added) {
            log.warn("数据队列已满，丢弃数据: {}", data.getKey());
        }
        
        // 如果队列达到批量大小，立即处理
        if (dataQueue.size() >= BATCH_SIZE) {
            CompletableFuture.runAsync(this::processBatch);
        }
        
        return added;
    }

    /**
     * 批量添加数据
     */
    public void addDataBatch(List<IorealData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        
        for (IorealData data : dataList) {
            if (!addData(data)) {
                break; // 队列满了就停止添加
            }
        }
    }

    /**
     * 处理批量数据
     */
    private void processBatch() {
        if (dataQueue.isEmpty()) {
            return;
        }
        
        List<IorealData> batch = new ArrayList<>(BATCH_SIZE);
        dataQueue.drainTo(batch, BATCH_SIZE);
        
        if (batch.isEmpty()) {
            return;
        }
        
        try {
            // 去重处理：同一个key只保留最新的数据
            Map<String, IorealData> uniqueData = new LinkedHashMap<>();
            for (IorealData data : batch) {
                uniqueData.put(data.getKey(), data);
            }
            
            List<IorealData> finalBatch = new ArrayList<>(uniqueData.values());
            
            // 执行批量更新
            long startTime = System.currentTimeMillis();
            batchUpdateWithRetry(finalBatch);
            long endTime = System.currentTimeMillis();
            
            totalProcessed.addAndGet(finalBatch.size());
            
            if (log.isDebugEnabled()) {
                log.debug("批量处理完成，数量: {}, 耗时: {}ms", finalBatch.size(), endTime - startTime);
            }
            
        } catch (Exception e) {
            totalFailed.addAndGet(batch.size());
            log.error("批量处理失败，数量: {}, 错误: {}", batch.size(), e.getMessage(), e);
        }
    }

    /**
     * 带重试的批量更新
     */
    private void batchUpdateWithRetry(List<IorealData> dataList) {
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount < MAX_RETRY_COUNT) {
            try {
                iorealDataMapper.saveOrUpdate(dataList);
                return; // 成功则返回
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                
                if (retryCount < MAX_RETRY_COUNT) {
                    try {
                        Thread.sleep(100 * retryCount); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("批量更新被中断", ie);
                    }
                }
            }
        }
        
        throw new RuntimeException("批量更新重试失败，重试次数: " + MAX_RETRY_COUNT, lastException);
    }

    /**
     * 强制刷新所有待处理数据
     */
    public void flush() {
        while (!dataQueue.isEmpty()) {
            processBatch();
        }
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("queueSize", dataQueue.size());
        stats.put("totalProcessed", totalProcessed.get());
        stats.put("totalFailed", totalFailed.get());
        stats.put("batchSize", BATCH_SIZE);
        stats.put("batchTimeout", BATCH_TIMEOUT_MS);
        stats.put("successRate", calculateSuccessRate());
        return stats;
    }

    /**
     * 计算成功率
     */
    private double calculateSuccessRate() {
        int total = totalProcessed.get() + totalFailed.get();
        if (total == 0) {
            return 100.0;
        }
        return (double) totalProcessed.get() / total * 100.0;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalProcessed.set(0);
        totalFailed.set(0);
        log.info("统计信息已重置");
    }
}
