<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesModulRecordMapper">

    <resultMap type="com.boyo.mes.entity.MesModulRecord" id="MesModulRecordResult">
        <result property="id" column="id"/>
        <result property="modulId" column="modul_id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="productionId" column="production_id"/>
        <result property="productionMultiple" column="production_multiple"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="loadTime" column="load_time"/>
        <result property="functionTime" column="function_time"/>
        <result property="productTimes" column="product_times"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>

        <result property="modulName" column="mould_name"></result>
        <result property="modulCode" column="mouldc_code"></result>
        <result property="equipmentName" column="equipment_name"></result>
        <result property="productionName" column="prodution_name"></result>
    </resultMap>
    <resultMap id="recordVO" type="com.boyo.mes.vo.ModuleRecordVO">
        <result property="id" column="id"></result>
        <result property="recordId" column="record_id"></result>
        <result property="equipmentName" column="equipment_name"></result>
        <result property="equipmentCode" column="equipment_code"></result>
        <result property="modulId" column="modul_id"></result>
        <result property="productionId" column="production_id"></result>
        <result property="startTime" column="start_time"></result>
        <result property="endTime" column="end_time"></result>
        <result property="mouldName" column="mould_name"></result>
        <result property="mouldcCode" column="mouldc_code"></result>
        <result property="productionName" column="production_name"></result>
        <result property="productionCode" column="production_code"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesModulRecordList" parameterType="com.boyo.mes.entity.MesModulRecord"
            resultMap="MesModulRecordResult">
        select * from( select t1.*,t2.mould_name,t2.mouldc_code,t3.materiel_name as prodution_name,t4.equipment_name
        from (select
        *
        from t_mes_modul_record
        <where>
            1=1
            and end_time is not null
            <if test="modulId != null">
                and modul_id = #{modulId}
            </if>
            <if test="equipmentId != null">
                and equipment_id = #{equipmentId}
            </if>
            <if test="productionId != null">
                and production_id = #{productionId}
            </if>
            <if test="productionMultiple != null">
                and production_multiple = #{productionMultiple}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="loadTime != null">
                and load_time = #{loadTime}
            </if>
            <if test="functionTime != null">
                and function_time = #{functionTime}
            </if>
            <if test="productTimes != null">
                and product_times = #{productTimes}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId}
            </if>
        </where>
        ) t1 left join t_mes_mould t2 on t1.modul_id = t2.id
        left join t_material t3 on t1.production_id = t3.id
        left join iot_equipment t4 on t1.equipment_id = t4.id
        ) ll
        <where>
            <if test="modulName != null">
                and ll.mould_name like CONCAT('%',#{modulName},'%')
            </if>
            <if test="modulCode != null">
                and ll.mouldc_code like CONCAT('%',#{modulCode},'%')
            </if>
        </where>
    </select>

    <select id="listCurrentModule" resultMap="recordVO">
        select t1.id,
               t1.equipment_name,
               t1.equipment_code,
               t2.modul_id,
               t2.production_id,
               t2.start_time,
               t2.end_time,
               t3.mould_name,
               t3.mouldc_code,
               t4.materiel_name as production_name,
               t4.materiel_code as production_code,
               t2.id as record_id
        from iot_equipment t1
                 left join (select * from t_mes_modul_record where end_time is null) t2 on
            t1.id = t2.equipment_id
                 left join t_mes_mould t3 on t2.modul_id = t3.id
                 left join t_material t4 on t2.production_id = t4.id
        order by t1.equipment_sort
    </select>
    <select id="listCurrentModule1" resultMap="MesModulRecordResult">
        select * from( select t1.*,t2.mould_name,t2.mouldc_code,t3.materiel_name as prodution_name,t4.equipment_name
                       from (select
                                 *
                             from t_mes_modul_record where end_time is  null and equipment_id = #{equipmentId}
                            ) t1 left join t_mes_mould t2 on t1.modul_id = t2.id
                                 left join t_material t3 on t1.production_id = t3.id
                                 left join iot_equipment t4 on t1.equipment_id = t4.id
                       ORDER BY equipment_name
                     ) ll
    </select>
</mapper>

