-- 性能测试脚本：比较优化前后的查询性能

-- 1. 测试原始查询（优化前）
SET @start_time = NOW(6);

SELECT m1.*, m2.val as lastVal, m2.update_time as lastUpdateTime
FROM (
    SELECT t1.*, t2.attr_name,
           t2.id as attr_id,
           t2.attr_code,
           t2.attr_desc,
           t2.attr_unit,
           t2.attr_order,
           t2.attr_type,
           t2.attr_class,
           COALESCE(l2.custom_multiple, t2.attr_multiple) as attr_multiple,
           l2.min_val AS minVal,
           l2.max_val AS maxVal,
           l2.enum_list AS enumList,
           l2.fault_val,
           l2.show_type AS showType
    FROM iot_equipment t1
    LEFT JOIN iot_tsl_attr t2 ON t1.tsl_id = t2.tsl_id
    LEFT JOIN iot_equipment_prop l2 ON t1.id = l2.equipment_id AND t2.id = l2.attr_id
) m1
LEFT JOIN iot_real_data m2 ON m1.equipment_code = m2.device_code AND m1.attr_code = m2.tag
ORDER BY 
    CASE WHEN m1.equipment_sort IS NULL THEN 1 ELSE 0 END,
    m1.equipment_sort,
    m1.id,
    m1.attr_order ASC
LIMIT 50;

SET @end_time = NOW(6);
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 AS '原始查询耗时(ms)';

-- 2. 测试优化后查询
SET @start_time = NOW(6);

SELECT 
    t1.id,
    t1.equipment_name,
    t1.equipment_code,
    t1.tsl_id,
    t1.equipment_img,
    t1.equipment_sort,
    t1.create_user_id,
    t1.dept_id,
    t1.open_id,
    t2.attr_name,
    t2.id as attr_id,
    t2.attr_code,
    t2.attr_desc,
    t2.attr_unit,
    t2.attr_order,
    t2.attr_type,
    t2.attr_class,
    t2.attr_multiple,
    m2.val as lastVal,
    m2.update_time as lastUpdateTime
FROM iot_equipment t1
INNER JOIN iot_tsl_attr t2 ON t1.tsl_id = t2.tsl_id
LEFT JOIN iot_real_data m2 ON t1.equipment_code = m2.device_code AND t2.attr_code = m2.tag
ORDER BY 
    CASE WHEN t1.equipment_sort IS NULL THEN 1 ELSE 0 END,
    t1.equipment_sort,
    t1.id,
    t2.attr_order ASC
LIMIT 50;

SET @end_time = NOW(6);
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 AS '优化后查询耗时(ms)';

-- 3. 显示索引使用情况
SHOW INDEX FROM iot_equipment WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM iot_equipment_prop WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM iot_real_data WHERE Key_name LIKE 'idx_%';
