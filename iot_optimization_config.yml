# IoT数据优化配置
iot:
  data:
    # 批量处理配置
    batch:
      enabled: true              # 是否启用批量处理
      size: 500                  # 批量大小
      timeout: 1000              # 批量超时时间(毫秒)
      threshold: 50              # 批量处理阈值
      queue-size: 10000          # 队列大小
      retry-count: 3             # 重试次数
    
    # 更新优化配置
    update:
      ratio: 0.8                 # 更新比例阈值(超过此比例使用更新优先策略)
      cache-refresh: 300000      # 缓存刷新间隔(毫秒)
    
    # 性能监控配置
    monitor:
      enabled: true              # 是否启用性能监控
      log-interval: 60000        # 日志输出间隔(毫秒)
      slow-query-threshold: 1000 # 慢查询阈值(毫秒)

# 数据库连接池优化（针对频繁更新）
spring:
  datasource:
    hikari:
      maximum-pool-size: 20      # 增加连接池大小
      minimum-idle: 10           # 最小空闲连接
      connection-timeout: 30000  # 连接超时
      idle-timeout: 600000       # 空闲超时
      max-lifetime: 1800000      # 最大生命周期
      leak-detection-threshold: 60000 # 连接泄漏检测

# MyBatis配置优化
mybatis-plus:
  configuration:
    # 批量操作优化
    default-executor-type: batch
    # 缓存配置
    cache-enabled: true
    local-cache-scope: statement
