package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipment;

import java.util.List;


/**
 * 物联网设备管理Mapper接口
 *
 * <AUTHOR>
 */
@Tenant
public interface IotEquipmentMapper extends BaseMapper<IotEquipment> {

    /**
     * 查询物联网设备管理列表
     *
     * @param iotEquipment 物联网设备管理
     * @return IotEquipment集合
     */
    List<IotEquipment> selectIotEquipmentList(IotEquipment iotEquipment);

    IotEquipment getEquipmentDetail(Integer id);

    List<IotEquipment> selectIotEquipmentListOnly(IotEquipment iotEquipment);

    /**
     * 优化版本：分页查询设备列表（减少JOIN操作）
     *
     * @param iotEquipment 查询条件
     * @return 设备列表
     */
    List<IotEquipment> selectIotEquipmentListOptimized(IotEquipment iotEquipment);

}
