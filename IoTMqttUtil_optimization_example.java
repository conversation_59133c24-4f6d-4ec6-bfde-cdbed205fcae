// 在 IoTMqttUtil 中使用优化后的方法示例

@Component
public class IoTMqttUtil {
    
    // 注入优化工具
    @Autowired
    private IotDataOptimizer iotDataOptimizer;
    
    // 原有的其他依赖...
    @Autowired
    private IorealDataMapper iorealDataMapper;
    
    // 示例：优化前的代码
    public void processDataOld(List<IorealData> tempList) {
        if (tempList.size() > 0) {
            // 原有方式：直接调用数据库
            iorealDataMapper.saveOrUpdate(tempList);
            equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
        }
    }
    
    // 示例：优化后的代码
    public void processDataOptimized(List<IorealData> tempList) {
        if (tempList.size() > 0) {
            // 优化方式1：使用智能优化器（推荐）
            iotDataOptimizer.saveOrUpdateOptimized(tempList);
            equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
        }
    }
    
    // 示例：使用异步批量处理
    public void processDataAsync(List<IorealData> tempList) {
        if (tempList.size() > 0) {
            // 优化方式2：直接使用批量服务
            iotDataOptimizer.addToBatch(tempList);
            // 注意：异步处理时，后续的故障处理可能需要调整时机
            equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
        }
    }
    
    // 在应用关闭时刷新剩余数据
    @PreDestroy
    public void onShutdown() {
        iotDataOptimizer.flush();
    }
}

// 具体的替换示例：

// 原代码：
/*
if (tempList.size() > 0) {
    iorealDataMapper.saveOrUpdate(tempList);
    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
}
*/

// 替换为：
/*
if (tempList.size() > 0) {
    iotDataOptimizer.saveOrUpdateOptimized(tempList);
    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
}
*/

// 或者对于高频更新场景，使用异步批量：
/*
if (tempList.size() > 0) {
    iotDataOptimizer.addToBatch(tempList);
    // 如果故障处理不依赖于数据库写入完成，可以继续执行
    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
}
*/
