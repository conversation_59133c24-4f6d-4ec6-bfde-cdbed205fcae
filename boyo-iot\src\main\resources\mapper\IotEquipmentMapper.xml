<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IotEquipmentMapper">

    <resultMap type="com.boyo.iot.domain.IotEquipment" id="IotEquipmentResult">
        <result property="id" column="id"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="equipmentCode" column="equipment_code"/>
        <result property="tslId" column="tsl_id"/>
        <result property="equipmentImg" column="equipment_img"/>
        <result property="equipmentSort" column="equipment_sort"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="openId" column="open_id"/>
        <association property="attrList" resultMap="IotTslAttrResult">

        </association>
    </resultMap>
    <resultMap type="com.boyo.iot.domain.IotTslAttr" id="IotTslAttrResult">
        <result property="attrName" column="attr_name"></result>
        <result property="attrCode" column="attr_code"></result>
        <result property="attrDesc" column="attr_desc"></result>
        <result property="attrUnit" column="attr_unit"></result>
        <result property="attrType" column="attr_type"></result>
        <result property="minVal" column="minVal"></result>
        <result property="maxVal" column="maxVal"></result>
        <result property="enumList" column="enumList"></result>
        <result property="showType" column="showType"></result>
        <result property="faultVal" column="fault_val"></result>
        <result property="lastVal" column="lastVal"></result>
        <result property="attrMultiple" column="attr_multiple"></result>
        <result property="attrClass" column="attr_class"></result>
        <result property="lastUpdateTime" column="lastUpdateTime"></result>
    </resultMap>


    <select id="selectIotEquipmentListOnly" parameterType="com.boyo.iot.domain.IotEquipment"
            resultMap="IotEquipmentResult">
        select *
        from iot_equipment
        <where>
            <if test="equipmentName != null  and equipmentName != ''">
                and equipment_name like concat('%', #{equipmentName}, '%')
            </if>
            <if test="equipmentCode != null  and equipmentCode != ''">
                and equipment_code = #{equipmentCode}
            </if>
            <if test="deptId != null  and deptId != ''">
                and dept_id = #{deptId}
            </if>
        </where>
    </select>
    <select id="selectIotEquipmentList" parameterType="com.boyo.iot.domain.IotEquipment" resultMap="IotEquipmentResult">
        select m1.*, m2.val as lastVal, m2.update_time as lastUpdateTime
        from (select t1.*,t2.attr_name,
        t2.id as
        attr_id,
        t2.attr_code,
        t2.attr_desc,
        t2.attr_unit,
        t2.attr_order,
        t2.attr_type,
        t2.attr_class,
        (case when l2.custom_multiple is null then t2.attr_multiple else l2.custom_multiple end) as attr_multiple,
        l2.min_val AS minVal,
        l2.max_val AS maxVal,
        l2.enum_list AS enumList,
        l2.fault_val,
        l2.show_type AS showType
        from (
        select * from iot_equipment
        <where>
            <if test="equipmentName != null  and equipmentName != ''">
                and equipment_name like concat('%', #{equipmentName}, '%')
            </if>
            <if test="equipmentCode != null  and equipmentCode != ''">
                and equipment_code = #{equipmentCode}
            </if>
            <if test="tslId != null  and tslId != ''">
                and tsl_id = #{tslId}
            </if>
        </where>
        )t1 left join iot_tsl_attr t2 on t1.tsl_id = t2.tsl_id
        left join iot_equipment_prop l2 on t1.id = l2.equipment_id and t2.id = l2.attr_id
        ) m1
        left join iot_real_data m2 on m1.equipment_code = m2.device_code and m1.attr_code = m2.tag
        order by m1.equipment_sort IS NULL,m1.equipment_sort,m1.id, m1.attr_order asc
    </select>

    <select id="getEquipmentDetail" resultMap="IotEquipmentResult">

        select m1.*, m2.val as lastVal, m2.update_time as lastUpdateTime
        from (select t1.*,
                     t2.attr_name,
                     t2.id        as
                                     attr_id,
                     t2.attr_code,
                     t2.attr_desc,
                     t2.attr_unit,
                     t2.attr_order,
                     t2.attr_type,
                     t2.attr_class,
                     (case when l2.custom_multiple is null then t2.attr_multiple else l2.custom_multiple end) as attr_multiple,
                     l2.min_val   AS minVal,
                     l2.max_val   AS maxVal,
                     l2.enum_list AS enumList,
                     l2.fault_val,
                     l2.show_type AS showType
              from (select *
                    from iot_equipment
                    where id = #{id}) t1
                       left join iot_tsl_attr t2 on t1.tsl_id = t2.tsl_id
                       left join iot_equipment_prop l2 on t1.id = l2.equipment_id and t2.id = l2.attr_id) m1
                 left join iot_real_data m2 on m1.equipment_code = m2.device_code and m1.attr_code = m2.tag
        order by m1.equipment_sort IS NULL, m1.equipment_sort, m1.id, m1.attr_order asc
    </select>
</mapper>
